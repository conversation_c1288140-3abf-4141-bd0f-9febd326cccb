---
title: Introducing AIBox
sidebar_position: 1
slug: /
---

## AIBox: Powering Community-Driven AI  

The AIBox is built for those who want to explore AI on their own terms. With 2 RX 7900 XTX GPUs and 48GB of memory, it enables running demanding AI models efficiently.  

## Open AI Development  

AIBox offers full control—no cloud restrictions, no unexpected costs. Train models, fine-tune AI systems, and experiment freely with PyTorch, TensorFlow, or low-level GPU programming.  

## More Than Hardware: A Shared Network  

AIBox isn’t just a tool—it’s part of a decentralized AI network. When idle, its GPU power can be shared via Mycelium, benefiting the wider community while generating value. Designed for efficiency, with water cooling and power monitoring, it’s a practical, community-powered step toward open AI development.  

## Expanding the ThreeFold Grid

Each AIBox integrates into the ThreeFold Grid, a decentralized Internet infrastructure active in over 50 countries. By connecting your AIBox, you contribute to this global network, enhancing its capacity and reach. This integration not only supports your AI endeavors but also strengthens a community-driven Internet ecosystem.

More info about threefold see: https://www.threefold.io

