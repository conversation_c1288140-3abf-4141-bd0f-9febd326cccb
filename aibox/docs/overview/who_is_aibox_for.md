---
title: Who Is AIBox For?
sidebar_position: 4
---

The AIBox is for hackers and AI explorers who want a simple, accessible gateway into AI experimentation, while also offering advanced features for those ready to push the boundaries of what's possible.

### Developers & Hackers
Technical capabilities:
- Direct GPU programming through ROCm
- Custom containerization support
- Full Linux kernel access
- P2P networking capabilities

### AI Researchers
Research-focused features:
- Support for popular ML frameworks (PyTorch, TensorFlow)
- Large model training capability (up to 48GB VRAM)
- Distributed training support
- Dataset management tools

### Tech Enthusiasts
Advanced features:
- Water cooling management interface
- Power consumption monitoring
- Performance benchmarking tools
- Resource allocation controls