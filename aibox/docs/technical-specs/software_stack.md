---
title: Software Stack
sidebar_position: 2
---

### ThreeFold Zero-OS

Zero-OS forms the foundation of AIBox's software architecture. Unlike traditional operating systems, it's a minimalist, security-focused platform optimized specifically for AI workloads and distributed computing.

Key features:
- Bare metal operating system with minimal overhead
- Zero overhead virtualization
- Secure boot process
- Automated resource management

This specialized operating system ensures maximum performance and security while eliminating unnecessary services and potential vulnerabilities.

### Mycelium Network Integration

The Mycelium Network integration transforms your AIBox from a standalone system into a node in a powerful distributed computing network based on peer-to-peer and end-to-end encrypted communication always choosing the shortest path.

### Pre-installed AI Frameworks

Your AIBox comes ready for development with a comprehensive AI software stack:

- ROCm 5.7+ ML stack
- PyTorch 2.1+ with GPU optimization
- TensorFlow 2.14+
- Pre-built container images