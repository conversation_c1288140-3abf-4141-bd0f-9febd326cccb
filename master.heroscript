# ThreeFold Documentation Master Configuration
# Orchestrates all documentation sections for the TFGrid4 project

# Main Documentation Site
!!docusaurus.site
    name:"main"
    config_path:"main/cfg/basic.heroscript"
    docs_path:"main/docs"
    priority:1
    description:"Core ThreeFold DePIN documentation"

# Technical Documentation
!!docusaurus.site
    name:"tech"
    config_path:"tech/cfg/basic.heroscript"
    docs_path:"tech/docs"
    priority:2
    description:"Technical specifications and architecture"

# Mycelium Network Documentation
!!docusaurus.site
    name:"mycelium"
    config_path:"mycelium/cfg/basic.heroscript"
    docs_path:"mycelium/docs"
    priority:3
    description:"End-to-end encrypted IPv6 overlay network"

# Business Documentation
!!docusaurus.site
    name:"business"
    config_path:"biz/cfg/basic.heroscript"
    docs_path:"biz/docs"
    priority:4
    description:"Business model and strategy documentation"

# Products Documentation
!!docusaurus.site
    name:"products"
    config_path:"products/cfg/basic.heroscript"
    docs_path:"products/docs"
    priority:5
    description:"Product-specific documentation"

# AI Box Documentation
!!docusaurus.site
    name:"aibox"
    config_path:"aibox/cfg/basic.heroscript"
    docs_path:"aibox/docs"
    priority:6
    description:"AI Box product documentation"

# New Internet Vision
!!docusaurus.site
    name:"newinternet"
    config_path:"newinternet/cfg/basic.heroscript"
    docs_path:"newinternet/docs"
    priority:7
    description:"Vision for the new internet"

# Technical Specifications
!!docusaurus.site
    name:"specs"
    config_path:"specs/cfg/basic.heroscript"
    docs_path:"specs/docs"
    priority:8
    description:"Detailed technical specifications"

# Global Build Configuration
!!docusaurus.build_all
    parallel:true
    environments:["dev", "prod"]
    notification_webhook:"https://hooks.slack.com/services/YOUR/WEBHOOK/URL"

# Global Deployment Settings
!!docusaurus.deploy_all
    ssh_connection:"<EMAIL>"
    base_path:"/root/hero/www"
    backup:true
    rollback_on_failure:true

# Cross-site Navigation Links
!!docusaurus.global_nav
    sites:[
        {"name": "Main Docs", "url": "https://docs.threefold.io", "description": "Core documentation"},
        {"name": "Tech Docs", "url": "https://threefold.info/tech/", "description": "Technical details"},
        {"name": "Mycelium", "url": "https://threefold.info/mycelium/", "description": "Network overlay"},
        {"name": "AI Box", "url": "https://aibox.threefold.io/", "description": "AI solutions"}
    ]

# Search Configuration (if using global search)
!!docusaurus.search
    provider:"algolia"
    index_all_sites:true
    api_key:"YOUR_ALGOLIA_API_KEY"
    index_name:"tfgrid-docs"

# Analytics Configuration
!!docusaurus.analytics
    google_analytics:"G-XXXXXXXXXX"
    track_all_sites:true
    custom_events:["download", "signup", "contact"]
