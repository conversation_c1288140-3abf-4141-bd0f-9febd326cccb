---
title: Network Comparison
sidebar_position: 3
---

## The Evolution of Network Architecture

The networking landscape is evolving beyond traditional centralized infrastructure toward more resilient, secure, and efficient solutions. The following compares ThreeFold's Mycelium IPv6 overlay network with traditional networking approaches, highlighting key architectural differences and their implications for security, scalability, and peer-to-peer connectivity.

## Traditional Network

The traditional network relies on centralized infrastructure, uses IPv4 addressing, and faces challenges with NAT traversal and unencrypted traffic by default.

### What is a Traditional Network?

Traditional networks are the standard internet infrastructure most people use today. These networks:

- **Centralized Infrastructure**: Rely on a hierarchical system of ISPs (Internet Service Providers), backbone providers, and core routers that control and direct traffic.
  
- **IPv4 Addressing**: Use the IPv4 protocol which has a limited address space (about 4.3 billion addresses). This scarcity led to workarounds like NAT.

- **NAT (Network Address Translation)**: A technique that allows multiple devices on a local network to share a single public IP address. While solving the IP address shortage problem, NAT creates significant challenges for direct peer-to-peer connections.

### What is NAT Traversal?

NAT traversal refers to the techniques used to establish connections between devices behind different NAT routers. Here's why it matters:

- **The NAT Problem**: When you're behind a NAT (like most home and office networks), other devices on the internet cannot directly initiate a connection to your device because your private IP address isn't publicly accessible.

- **Connection Challenges**: For applications requiring direct connections (like video calls, online gaming, or file sharing), complex workarounds are needed:
  - STUN (Session Traversal Utilities for NAT): Helps devices discover their public IP and port mappings
  - TURN (Traversal Using Relays around NAT): Uses relay servers when direct connections fail
  - ICE (Interactive Connectivity Establishment): Combines multiple techniques to find the best connection path

- **Reliance on Intermediaries**: Often requires centralized servers to facilitate connections, creating potential points of failure and privacy concerns.

### Other Traditional Network Limitations

- **Unencrypted by Default**: Most traditional network traffic is not automatically encrypted, requiring additional protocols (like HTTPS, VPNs) for security.

- **Routing Inefficiency**: Traffic often takes suboptimal paths through centralized infrastructure rather than direct routes between peers.

- **Vulnerability to Censorship**: Centralized control points can be leveraged to monitor, filter, or block traffic.

### Visual Representation

This diagram illustrates the traditional network architecture with its centralized infrastructure, unencrypted traffic flows, and NAT traversal challenges between users.

```mermaid
graph TB
    subgraph "Traditional Network"
        A[User A] -->|Unencrypted Traffic| R1[ISP Router]
        B[User B] -->|Unencrypted Traffic| R2[ISP Router]
        R1 -->|Public Internet| R3[Core Router]
        R2 -->|Public Internet| R3
        R3 -->|NAT Traversal Issues| R1
        R3 -->|NAT Traversal Issues| R2
        R1 -->|IPv4 Address Space| A
        R2 -->|IPv4 Address Space| B
        style A fill:#f9f,stroke:#333,stroke-width:2px,color:#600
        style B fill:#f9f,stroke:#333,stroke-width:2px,color:#600
        style R1 fill:#bbf,stroke:#333,stroke-width:2px,color:#006
        style R2 fill:#bbf,stroke:#333,stroke-width:2px,color:#006
        style R3 fill:#bbf,stroke:#333,stroke-width:2px,color:#006
    end
```

## Mycelium IPv6 Overlay Network

ThreeFold's Mycelium network provides end-to-end encryption, locality-aware routing, and direct P2P connections with automatic rerouting capabilities.

### What is an Overlay Network?

An overlay network is a virtual network built on top of existing network infrastructure. Mycelium creates a secure, efficient layer that operates independently of the underlying physical network:

- **Virtual Network Layer**: Functions as a separate network with its own addressing, routing, and security protocols while still using the physical internet for data transport.

- **End-to-End Encryption**: Unlike traditional networks, Mycelium encrypts all traffic between nodes by default, ensuring privacy and security without requiring additional protocols.

- **IPv6 Addressing with 400::/7 Range**: Uses a specific range of IPv6 addresses (which offers an astronomically larger address space than IPv4), eliminating the need for NAT and its associated problems.

### How Mycelium Solves NAT Traversal

Mycelium fundamentally changes how devices connect by:

- **Identity-Based Networking**: Each node's IPv6 address is cryptographically linked to its private key, creating a verifiable identity that works regardless of the underlying network configuration.

- **NAT Penetration**: Automatically establishes connections between nodes behind different NATs without requiring complex STUN/TURN/ICE protocols or centralized servers.

- **Direct P2P Communication**: Enables true peer-to-peer connections without intermediaries, even when both parties are behind NAT routers.

- **Relay Fallback**: In the rare cases where direct connection is impossible, Mycelium can use relay nodes while maintaining end-to-end encryption, preserving privacy.

### Advanced Networking Features

- **Locality-Aware Routing**: Intelligently routes traffic based on network proximity, reducing latency and improving performance compared to traditional routing.

- **Automatic Rerouting**: If a network path fails or becomes congested, Mycelium automatically finds alternative routes without disrupting the connection.

- **Resilience**: Distributed architecture with no single points of failure makes the network resistant to outages and censorship attempts.

- **Scalability**: The design can scale to billions of devices without the addressing and connection limitations of traditional networks.


### Visual Representation

This diagram shows how Mycelium creates a secure overlay network with end-to-end encryption, direct P2P connections, and fault-tolerant routing between nodes.

```mermaid
graph TB
    subgraph "Mycelium IPv6 Overlay Network"
        M1[Node A] -->|End-to-End Encrypted| M3[Relay Node]
        M2[Node B] -->|End-to-End Encrypted| M3
        M3 -->|Locality Aware Routing| M1
        M3 -->|Locality Aware Routing| M2
        M1 -->|Direct P2P Connection| M2
        M1 -->|400::/7 IPv6 Range| M4[Private Key]
        M2 -->|400::/7 IPv6 Range| M5[Private Key]
        M3 -->|Automatic Rerouting| M6[Alternative Path]
        M6 -->|Fault Tolerance| M1
        M6 -->|Fault Tolerance| M2
        style M1 fill:#bfb,stroke:#333,stroke-width:2px,color:#060
        style M2 fill:#bfb,stroke:#333,stroke-width:2px,color:#060
        style M3 fill:#bfb,stroke:#333,stroke-width:2px,color:#060
        style M4 fill:#fbb,stroke:#333,stroke-width:2px,color:#600
        style M5 fill:#fbb,stroke:#333,stroke-width:2px,color:#600
        style M6 fill:#bfb,stroke:#333,stroke-width:2px,color:#060
    end
```

## Key Differences


This graph highlights the fundamental differences between traditional networks and Mycelium's approach, contrasting IPv4 vs IPv6 addressing, centralization vs P2P architecture, and security features.

```mermaid
graph TB
    subgraph "Key Differences"
        K1[Traditional Network] -->|Uses| K2[IPv4 Addressing]
        K1 -->|Relies on| K3[Centralized Infrastructure]
        K1 -->|Has| K4[NAT Traversal Challenges]
        K1 -->|Often| K5[Unencrypted by Default]
        
        K6[Mycelium Network] -->|Uses| K7[IPv6 400::/7 Range]
        K6 -->|Features| K8[End-to-End Encryption]
        K6 -->|Enables| K9[P2P Direct Communication]
        K6 -->|Provides| K10[Automatic Rerouting]
        K6 -->|Offers| K11[Locality Awareness]
        K6 -->|Links| K12[IP to Private Key]
        
        style K1 fill:#ddd,stroke:#333,stroke-width:1px,color:#333
        style K6 fill:#ddd,stroke:#333,stroke-width:1px,color:#333
        style K2 fill:#fdd,stroke:#333,stroke-width:1px,color:#900
        style K3 fill:#fdd,stroke:#333,stroke-width:1px,color:#900
        style K4 fill:#fdd,stroke:#333,stroke-width:1px,color:#900
        style K5 fill:#fdd,stroke:#333,stroke-width:1px,color:#900
        style K7 fill:#dfd,stroke:#333,stroke-width:1px,color:#060
        style K8 fill:#dfd,stroke:#333,stroke-width:1px,color:#060
        style K9 fill:#dfd,stroke:#333,stroke-width:1px,color:#060
        style K10 fill:#dfd,stroke:#333,stroke-width:1px,color:#060
        style K11 fill:#dfd,stroke:#333,stroke-width:1px,color:#060
        style K12 fill:#dfd,stroke:#333,stroke-width:1px,color:#060
    end
```


## Key Features of Mycelium
- **End-to-End Encryption**: All traffic between nodes is encrypted
- **IPv6 Addressing**: Uses the 400::/7 IPv6 range
- **Identity-Based**: IP addresses are linked to private keys
- **Locality Awareness**: Finds the shortest path between nodes
- **Automatic Rerouting**: Reroutes traffic if a physical link fails
- **P2P Communication**: Works on NAT networks without middlemen
- **Scalable Design**: Built to scale to planetary level

## Traditional Network Limitations
- Primarily uses IPv4 addressing with limited address space
- Often requires NAT traversal for peer-to-peer connections
- Traffic is not encrypted by default
- Relies on centralized infrastructure
- Less resilient to network failures
- Limited direct peer-to-peer communication capabilities

## Building a New Internet with Mycelium

The Mycelium IPv6 overlay network represents a significant advancement over traditional networking paradigms by addressing fundamental limitations in security, addressing, and connectivity. By implementing end-to-end encryption, identity-based IPv6 addressing, and locality-aware routing, Mycelium creates a more secure, resilient, and efficient network infrastructure that enables true peer-to-peer communication without the constraints of NAT traversal or centralized control points.

Looking ahead, we are building upon the Mycelium network foundation to create an entirely new internet architecture. This next-generation infrastructure will feature decentralized DNS systems that resist censorship, distributed content delivery networks (CDNs) that optimize data transfer, advanced data storage and retrieval mechanisms, secure peer-to-peer communication platforms, and quantum-safe storage solutions that protect data for decades to come. Additionally, the architecture will provide specialized entry and exit points for AI workloads and seamless frontend/backend integration capabilities. 

The technology we've built today is just the beginning of a transformative journey toward a more secure, private, and resilient digital world. We invite you to join us in shaping this future where connectivity empowers rather than constrains, where privacy is a fundamental right rather than an afterthought, and where the internet truly belongs to everyone.