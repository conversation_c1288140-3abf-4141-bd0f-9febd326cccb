---
title: Name Services
sidebar_position: 5
---

# Decentralized Name Services in Mycelium

## Introduction

Mycelium integrates a decentralized name service, leveraging blockchain technology to provide a resilient and fair system for domain registration and resolution. Unlike traditional DNS, which is controlled by a few centralized entities, Mycelium’s name service ensures that names are securely and transparently registered on a blockchain, preventing censorship and single points of failure.

## Key Benefits of Mycelium's Name Services on ThreeFold

### Geo Load Balancing

Mycelium’s name service enables geo-load balancing by directing traffic to the closest available server based on the user's geographic location. This ensures optimal performance, reduces latency, and enhances the user experience by serving content from the nearest node.

### Failover Protection

In the event that one site or server becomes unavailable, Mycelium’s name service provides automatic failover by redirecting traffic to alternative available nodes. This ensures continuous service availability and prevents downtime from affecting end users.

### Decentralized and Fair Name Distribution

Traditional domain registration systems often lead to monopolization, with valuable names being hoarded or controlled by a few entities. Mycelium's blockchain-based name service ensures a more equitable distribution of domain names, allowing everyone fair access to meaningful names without centralized control or speculation.

## How It Works

- Names are registered on a decentralized blockchain ledger, making them immutable and censorship-resistant.
- The network dynamically resolves domain names using decentralized consensus mechanisms, eliminating reliance on traditional DNS infrastructure.
- The system optimizes name resolution based on real-time network conditions, ensuring efficiency and reliability.

By integrating decentralized name services, Mycelium empowers users with greater control over their digital identities and creates a more resilient, efficient, and censorship-resistant Internet infrastructure.