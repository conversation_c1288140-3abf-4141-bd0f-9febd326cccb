---
title: Governance
sidebar_position: 15
---


## DAO

Each Regional Internet is managed by a DAO (Decentralised Autonomous Organization).

Participants of the DAO

* Users
  * Everyone who uses capacity of the Regional Internet = TFGrid
* Farmers
  * Everyone who provides capacity to the Regional Internet
* Technology Council
  * Up to 12 people who audit and control software releases (how do we upgrade the grid)
  * Note: Selection process has not been defined but will be decided upon with the community. We may ask the communities who might be interested and then create a short list and put it to the DAO for a vote.
* Wisdom Council
  * Up to 12 people with the purpose to safeguard our values and provide additional protection
  * Note: Selection process to be defined as well. Values and experience doing well for the world will be important. Requirements for council membership need to be defined and agreed upon with the community.

The 2 councils can

* Create Voting Requests (needs majority)

* Veto votes (needs 3 out of 12 and then min 6 out of 12 to confirm). This is like our emergency brake.

Voting requests are needed for

* Change any of the parameters or structures of the rewards for the farmers or any changes to the “smart contracts” as used on the ThreeFold Chain (the mechanism of how everything works).
* Approve release of code (when and how to upgrade our software components).
* Changing Members of one of the Councils
* Approve spending of money from the treasury (e.g. release for improvement of TFGrid)
* Changing parameters of the Treasury Liquidity Pool
* Additional discounts for a solution provider

## Auditing

TFGrid Validators run automated algorithms to check the correct behavior and authenticity of the different participants in the TFGrid ecosystem (farmers, users, etc.) in relation to the parameters as set in TFChain and the expected behavior. The validators are also responsible for consolidating the IOU Payments and referencing them against the information they already have about the reputation of the different participants in a transaction.
