---
title: Imagine a New Internet
sidebar_position: 0
slug: /
---

![new-internet](./img/1_new_internet.png)

Imagine a world where YOU are the Internet.

Imagine a world where communities can build and own their own Internets from scratch – which would deliver all the needs for a better future life.

This Internet is here! It includes:

* A regenerative, safe, sovereign Internet infrastructure layer = ThreeFold (TFGrid).
* A set of experiences (applications) and information to allow everyone on an equal basis to learn,
create, exchange, travel, … basically to experience an abundance-based world.

**Abundance is our baseline:**

* An abundance of free information, education, and knowledge (e.g. books, journals, studies), information about our planet (e.g. maps, databases), and creativity (e.g. music, video, digital art, immersive museums).
* Access to all applications required for your personal and professional digital life.
* You can safely communicate with the world.
* You can freely exchange anything for anything else (money for goods, goods for goods, money for money, etc.).
* No need for manipulative marketing – you can find anything for free and promote everything for free.

This decentralized Internet lets you co-own a supercomputer at the edge, which gives you unlimited access to advanced digital experiences in high quality, such as augmented reality (education, shopping, …), virtual reality (online meetings, travel, concerts, …), metaverse (a metaverse owned by you), and more.

You can keep your full digital life history in your personal ultra-secure archive. All the data is yours, forever.

Today's Internet is broken. The promise of a neutral peer-to-peer network connecting the entire planet remains unfulfilled. Instead, people became products and data became like digital oil, fueling another extractive and unsustainable industry.

This Internet largely relies on massive centralized data centers that exist only in a handful of regions and are mostly controlled by a handful of large corporations. Nearly half the planet remains unconnected, and the current model can't scale with ever-increasing demand from those who are connected.

While blockchain and web3 represent an important improvement for the future of the Internet, these technologies are not a complete solution. The next generation Internet will need to handle massive amounts of data and computation in a way that's independent from the legacy web2 infrastructure and the corporations who own it.

ThreeFold enables anyone to participate in the creation and the utilization of a regenerative, safe, and sovereign Internet infrastructure. Upon that foundation, any digital experience or application can be hosted in a way that's efficient and secure.

People today can become co-owners of a powerful decentralized computing network by connecting their hardware to a network made possible by ThreeFold's unique software, including a unique autonomous operating system. Likewise, individual communities can form their own independent Internet network that serves as one node in a global mesh. ThreeFold has the potential to serve every person on Earth.

The baseline of this model is both abundance and freedom: abundance of knowledge and digital resources as well as freedom to access and publish information. Simultaneously, it allows individuals and groups to really own and control their data, in a way that's private and indestructible. The original promise of the Internet: a peer-to-peer participatory network that can meet the needs of all of humanity for generations to come.

The TFGrid, in essence, is built of 3Nodes and 3Bots. 3Nodes are computers which run our operating system called Zero-OS and provide a given amount of compute, network and storage capacity. Farmers (miners) receive rewards for hosting a 3Node and connecting to the standard Internet. 3Bots are personal gateways providing access to the decentralized web3 universe while also making sure that the data of users can never get corrupted nor lost.

This new Internet, as we call it, is fully compatible with the existing one. Indeed, ThreeFold has successfully developed a peer-to-peer cloud operating system running on bare hardware, directly using the computer native resources.

Our aim is to build a robust, sustainable, and genuinely decentralized Internet capacity layer, including storage and computing resources, for any blockchain project to thrive on, and much more. The following document will outline in further detail the possibilities that ThreeFold offers.
