---
title: Missing Layer
sidebar_position: 18
---

## Develop For 3Bot versus Your Own Apps On Capacity Layer (TFGrid)

![new-internet](./img/new-internet-36.png)

Our tech is the missing layer for blockchain, storage, compute and GPU workloads.

Zero-OS is used on the 3Nodes and provides the base layer with storage, compute & network capacity. See below for more information on Zero-OS.

The Twins are a new development paradigm for building ultra scalable & sovereign Internet dapps of the future. We use this ourselves to deploy the projects we are doing with large banks and some countries who are ready for a more regenerative Internet future for their citizens.

Thanks to our Smart Contract of IT layer, you can still use industry standard tools like Kubernetes, Helm, Terraform, and more, to deploy any workload you would want. Our solution is compatible with anything which can run on Linux and our additional storage and network primitives provide additional benefits.

## Tech: Zero-OS = blockchain driven secure operating system

Our decentralized operating system called Zero-OS allows every company or person in the world to farm (mine)
Internet compute, storage or network capacity which can be used by everyone to host their Internet solutions.

![new-internet](./img/new-internet-37.png)

Our blockchain provisioning/utilization layer allows everyone in the world to provide or consume compute, storage and network capacity in full consensus. There are multiple possibilities on how to interact with the capacity layer of the ThreeFold Grid.

![new-internet](./img/new-internet-38.png)

ThreeFold 3Nodes (the computers that provide the storage, compute and network capacity), can be as small as a 100 USD personal computer or as big as a 50 000 USD server in a data center. There is no limit to the scale we can achieve.

Each layer is as distributed as possible: already today a lot of capacity has been deployed in the world. See more here: https://stats.grid.tf.

## Tech: Our Quantum Safe Storage System Protects Your Data

![new-internet](./img/new-internet-39.png)

Your data is unlimitedly scalable and can never be lost or corrupted. You are the only one who owns your data.

Even if 3Nodes or data centers go down, your data will still be there and available forever.

![new-internet](./img/new-internet-40.png)

Your quantum safe storage system is very cost effective, the cost of farming 1 TB of storage per month can be less than 1 USD, while the market price is between 20 and 180 USD.

Our storage system is the result of 20 years of engineering that went through many iterations. Our last company was acquired for 300m USD for a subset of this technology.

## Tech: Our Planetary Network, Mycelium, Provides Ultimate Connectivity

Our network layer connects anything to anyone. We have clients for iPhone, Android, Windows, Linux and OS.

The end-to-end encrypted network will look for the shortest path between any app or user. It has a built-in DNS system and works seamlessly together with existing networks like the current Internet backbones and providers, satellite providers like Starlink, 5G, meshed wireless or any other mobile technology. It is the swiss army knife for network connectivity in the world which tries to keep our data flowing all the time in all security. Communities can even build their own networks with meshed wireless technology.

![new-internet](./img/new-internet-41.png)

Your network address is unique to your device and you, and it never changes and it doesn’t matter how you will communicate or where you are. If links go down, the Planetary network client will look for other paths.

## Tech: 3Bot Technology

![new-internet](./img/new-internet-42.png)

Each 3Bot is hosted on the ThreeFold Grid. The data is using the quantum safe storage layer and can never be corrupted nor lost. This architecture is super scalable where billions of 3Bots can be deployed.

![new-internet](./img/new-internet-43.png)

![new-internet](./img/new-internet-44.png)

Each 3Bot talks directly to the other 3Bots. This keeps information flow local and provides for a greener Internet experience. It also improves security, performance and privacy.

The 3Bot is also compatible with IPFS to integrate with the web3 world.

![new-internet](./img/new-internet-45.png)

![new-internet](./img/new-internet-46.png)

Each 3Bot can talk to any blockchain around (web3) to arrange management of digital currencies and peer2peer exchange of value. The 3Bots are compatible with any major blockchain technology.

The 3Bots can also talk to web 2 applications and own deployed client-server applications on top of the TFGrid. This leads to a super scalable and polyvalent ecosystem.

![new-internet](./img/new-internet-47.png)
