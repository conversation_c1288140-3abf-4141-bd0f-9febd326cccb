---
title: Internet of Internets Architecture
sidebar_position: 14
---


* We have developed a massively scalable Internet concept composed of 3 layers. The ThreeFold Internet of Internets approach needs to scale to billions of users, and needs to be available at all times. We believe that the best way is not to have only one Internet, but many Internets, hence the name Internet of Internets.

![new-internet](./img/new-internet-26.png)

The above image shows how the Internet is hierarchical and scales forever. Each layer has a distinct purpose and allows the ecosystem to interact. While being hierarchical, it is still 100% peer-to-peer and decentralized.

Let's look at the different layers:

### Layer 2 = Internet of Internets = Mother DAI


The top layer and the base governance layer.

* Secured by 100 L2 validators

  * The validator is funded and managed by a person who staked tokens (staking means locked to the validator) to secure the validator. The 100 validators together need to find consensus about the transactions happening.
  * Each validator needs 2m tokens, growing to 3m tokens. Everyone in the world can delegate their stake to these validators.
* The following functions are implemented on the Mother DAI

  * Holds the tokens, is our main digital currency blockchain.
  * The token is the digital currency used to reward Farmers as well as allow people to consume computer/storage/application/network capacity from the grid.
  * DAI = Decentralized Autonomous Internet: each validator has a certain amount of votes
and can vote on decisions related to the functions of the blockchain.
  * Reward system for the L2 validators: validators receive a reward for hosting the validator.

Everyone in the world can use the tokens to buy capacity from the Regional Internets with just one currency (more information below).
5% of these proceeds go to the Validators. This is the global protection layer for all Regional Internets.

Each Regional Internet will have a read-only Validator as part of their Region, this is to make sure that the Layer 1 can never go down, not even if the Global Internet gets cut in pieces because of war, natural disasters or countries cutting off their Internet.

### Layer 1 = OurWorld Regional Internet

Every community in the world can deploy their own Regional OurWorld Internet, a regional Internet that scales for millions of users. The Regional Internet has its own blockchain and can operate 100% independently from the other Regional Internets. Of course, lots of connections are made between the different Regional Internets.

A Regional Internet starts with a maximum of 99 founding fathers or mothers who deploy blockchain nodes with integrated support for DEFI. Each Regional Internet has its own ThreeFold Token (colored version), differences in farming & cultivation model are possible. Each Regional Internet has a DEFI pool to allow frictionless exchange between the master token and the token of each region.

This Regional Internet enables

* A GDP-positive system for the country the Regional Internet is hosted in.
* Independence, sovereignty, security, green, reliability, performance, etc. There is no need to build more data centers.
* A new Internet infrastructure platform (fully peer-to-peer, decentralized, and super reliable)
* A new decentralized finance & digital asset exchange system, based on values.
* A set of incredible experiences, with the ability to provide digital freedom for all our online requirements.
* An upgraded economic system platform: Web 4.0, a new way to communicate, share and collaborate.

We want to make it easy for everyone to get started (as part of our latest fundraising project)

* A full ebook (hundreds of pages) with all details information as needed to deploy a Regional Internet.
* Train the Trainer programs, to allow everyone to install their own Regional Internet.
* Augmented/video education on how to deploy such a system.

A Regional Internet comes pre-populated (as part of last fundraise project).

* We are executing on a huge effort to download and categorize +40 Petabytes of information from the existing Internet. This information will be available in each OurWorld Instance, making sure that all this information is protected and available to everyone in the world independent of what is or will happen.
* We collect endless amounts of information (maps, educational content, source code, knowledge, ebooks, etc.).

Components of the Regional Internet

* Regional Internet Blockchain (RIB)
  * 9-99 validators (secures the local Internet)
  * Hosts a local token (colored token with own price)
  * DAO for management of the local OurWorld Internet
* A DEFI system, allowing regional economic activities to take place (H1 2023)
* Initial Internet capacity to host the base Information & Educational Layer (+40 Petabytes needed at this stage)

### Layer 0 = Neighborhood Internet Layer = Neighborhood Cloud

This is the layer where the actual Internet capacity is being generated and where communities together co-own their Internet.

There are 4 ways how this typically happens

* DIY = do-it-yourself farmers expand the Internet. This is happening today: lots of farmers add capacity every day.
* Certified 3Nodes - Pre-built 3Nodes that offer increased security and are plug-and-earn.
  * Neighborhood real estate deals: together with real estate developers, Internet capacity is added to the homes.
* Neighborhood Commercial Property: Internet capacity can be added afterwards to add value.

Each Neighborhood Cloud has the following parts:

* Internet Infrastructure (Compute, Storage & Compute), usable for any possible workload.
  * “Be Your Own Internet”
  * Data sovereignty, safety, green, high performance, cost efficiency, …
* Web 4 Layer, running on top of the Internet Infrastructure
  * Finance / Business Layer
* The base layer to allow everyone to develop on top of the Neighborhood Cloud

The Following Experiences are by default available for each OurWorld Internet

* Sikana (education system with lots of content, +4 billion views today)
* FreeFlow Twin (an alternative to Office 365 and Google Docs/Drive)
* Alternatives to Twitter, WhatsApp, Facebook, etc.
* An abundance-based sharing app: everyone can exchange goods with each other
* So much more…
