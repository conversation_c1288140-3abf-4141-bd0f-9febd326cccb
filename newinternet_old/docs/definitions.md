---
title: Definitions
sidebar_position: 21
---


## 3BOT

Your personal digital private assistant to help you organize your Internet Life.

The 3Bot helps you to manage your calendar, personal data and files, personal communication, digital money, to deploy required solutions or applications on top of the TFGrid, to participate in the DAO, to communicate in an easy and uniform way to the WEB2 and WEB3 world, and so much more.

## 3NODE

The computers that extend the TFGrid with compute, storage and network capacity. 3Nodes are connected to each other using the standard Internet connectivity or peer-to-peer connections (meshed wireless, local fiber mesh, etc.).

## Founding Members

Those are the people who together decide to create a Regional Internet.
These members will also define the initial bootstrap parameters of the TFChain:

* The amount of tokens which is printed as part of the creation of the Regional Internet.
This can never be more than 10% of the maximum tokens of a Regional Internet.
* The maximum tokens in a regional Internet (Typically 1 Billion).
* Reward per validator (X nr of tokens per validator + % of tokens burning).
* The initial price of a token, and how many tokens (maximum/minimum) will be sold at this price and for a given time period.
* Any other parameters as may be required by the TFChain.

The Founding Members need to find at least 50 members who are willing to host a validator node. These 50 validator nodes are the foundation of a Community (or Regional) Internet.

## Community (or Regional) Internet

An Internet as owned by a community. This community may be located within a certain geographical area or united more so by beliefs, values, or mission. The founding members of the community run the initial validator nodes for this Internet and its corresponding TFGrid and TFChain.

## TFGrid

The Technical Infrastructure layer of a Regional Internet, is the network of the 3Nodes and the TFChain managing the governance and consensus primitives of the TFGrid

## TFGrid Universe

A lot of services as provided on the TFGrid are fully decentralized and not stored on any centralized or even blockchain mechanism. To query the state of these services or ask to be provided a service, the 3Bot will, on behalf of the user, communicate over the TFGrid Universe, a peer-to-peer network which allows finding information as well as reliably asking for services. Even payments and invoicing happens over this mechanism. This allows for ultimate scale and reliability.

## TFChain

The blockchain governing a TFGrid.

## TF Mother Chain

The mother of all TFChains, this is where we find the token reserve currency and the DAO taking decisions which are relevant for all regional Internets. On this chain, a regional Internet needs to be registered and the TFChain Genesis Contract per regional Internet needs to be approved.

To register a regional Internet, enough token needs to be staked on the TF Mother Chain.

## TFChain Genesis Contract

The Founding Members of a Regional Internet define the specifics of the Regional Internet and its TFGrid.
These specifics are defined in a TFChain Genesis Contract. See above in “Founding Members” to learn more about how this works.

## TFChain Treasury

It is a systems wallet as owned by the TFChain itself.

The treasury is used to

* Allow people with tokens to buy the local tokens (e.g. to be able to stake, or buy capacity).
* Allows people with local tokens to buy tokens to use in other regional Internets.
* Allows the DAO to reward development work or other beneficial actions for the TFGrid.
* Allows the DAO to burn tokens if there would be too many in the treasury.

A certain percentage coming from utilization of the TFGrid goes to the TFChain Treasury.

## TFChain Treasury Liquidity Pool

A pool in which Regional Internet members can pool their liquidities in the form of ThreeFold tokens and potentially other currencies. This provides liquidity and it allows easy exchange between these currency pairs. A part of the Treasury might be used (as voted by DAO) to fund the liquidity pool.

## TFGrid Validators

Those are nodes running the TFChain and executing on Auditing and TFGrid Verification jobs.

Initial Founders of a Regional Internet need to stake tokens to let the validator nodes function. Running a validator node results in receiving rewards in tokens.

The validators do the following

* Guarantee the TFChain correct working (proof of stake blockchain).
* Audit the capacity provided by the TFGrid Farmers (happens by means of automatically-generated auditing code which gets executed on 3Nodes randomly, this code will verify the state, available capacity and utilization of a node This allows the TFGrid to find bad actors).
* Verification and roll-up of P2P Transactions to OnChain Transactions.
