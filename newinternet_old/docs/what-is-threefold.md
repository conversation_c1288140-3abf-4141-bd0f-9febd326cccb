---
title: What is ThreeFold?
sidebar_position: 5
---


ThreeFold provides a new way to deploy Internet capacity (storage, compute and network). Internet capacity Providers (we call them Farmers) deploy capacity, and developers use this capacity. Applications can be deployed on this network of capacity in neutrality and with data sovereignty. The aim of the ThreeFold project is to provide an alternative Internet experience which delivers:

* Green = up to 100x energy savings (by using the 3Bot concept)
* Neutral Search = find & publish all information without manipulation
* Help bring the Internet to the 3 billion people who don’t have reasonable access yet.
* Allow everyone to build on top (open source), equality is important
* Give everyone their personal 3Bot to make the Internet more easy and safe to use without the need for manipulation and addictions. The Internet should be a tool, not take over our life.

Today, the technology is live in its 3rd generation (with version 4 coming later in 2023), and the TFGrid has been connected in more than 60 countries across the globe:

![new-internet](./img/new-internet-9.png)

## ThreeFold Architecture

The ThreeFold Grid is the co-owned infrastructure layer, and can be seen as a decentralized cloud. Farmers provide storage, compute, and network capacity to the people around them and earn for it. Farmers can be commercial entities or anyone using our open-source software to deploy nodes in their homes, schools and offices.

3Bots live on top of the ThreeFold Grid and act as Web2 and Web3 gateways as well as self-healing bots for IT workloads. They are private virtual assistants that help users achieve a convenient, secure, and authentic digital life. They assist Web3 developers in achieving quicker results by alleviating concerns related to decentralization, scalability, storage stability, and performance, among other things.

3Bots communicate directly with each other and the rest of the world (existing centralized services) as well as existing blockchain technologies. The data is stored using a novel storage technology which makes sure it is safe and can never get lost, yet it is only owned by you and not even a quantum computer can hack it.

![new-internet](./img/new-internet-10.png)

## 3NODE, Your contribution to a Fair Internet

Together we have the chance to build a new Internet, which consists of a network of 3Nodes. A 3Node is a standard computer which runs our Operating System called Zero-OS.

![new-internet](./img/new-internet-11.png)

Your 3Node has a certain amount of compute, network and storage capacity, a Farmer (miner) receives rewards for hosting a 3Node and connecting to the standard Internet. Today, you can become a farmer on our global proof of concept TFGrid network (version 3.0). See https://threefold.io/farm/ for more information. As of now, about 60,000 virtual CPU cores and 25,000,000 GB of storage are active on the TFGrid.

A 3Node boots over the Internet network and runs our own stateless Zero-OS operating system. The Zero-OS allows the deployment of any IT workload in a safe way. Not only that, but any IT workload which can run on Linux is supported. Zero-OS supports many compute, storage and network primitives (virtual machines, deduped filesystems, quantum safe filesystem, planetary-scalable networks, containers, Kubernetes, etc.).

![new-internet](./img/new-internet-12.png)

Zero-OS allows for a more secure experience and better efficiency in using the computer hardware.

## 3BOT, A Personal Gateway to the Web3 World

We imagine a future where every user can have their own 3Bot serving as a personal gateway to their decentralized web3 universe. This 3Bot functions as your virtual assistant, simplifying your digital life.

Currently, developing for Web3 requires significant expertise. Developers typically need access to API gateways, which are often supplied by commercial companies. They must familiarize themselves with a variety of blockchain technologies and different types of interfaces before they can produce good results. The 3Bot allows the developer to interact with one high-level simple interface. Moreover, the 3Bot will further communicate with different Blockchain Networks and Technologies as well as the older Web2 protocols like smtp, imap, webdav, caldav, carddav, ftp, etc.. The 3Bot will also make sure that the data of users can never get corrupted nor lost.

![new-internet](./img/new-internet-13.png)

In this architecture, every person owns a 3Bot. This gives ultimate scalability & flexibility. Development time can be reduced 3x at least, while maintaining 100% compatibility with the existing Internet and Web3 world.

A 3Bot has following capabilities:

* Your personal gateway to the Ethereum and wider web3 ecosystems.
* Your personal fully integrated BTC and BTC Lightning Node with extra tools.
* A chat interface to communicate with your 3Bot as well as Nostr & Matrix users.
* A personal data store for all your apps, which can scale to petabytes, is 100% private and unbreakable.
* Deploy block chain nodes or any other decentralized application on top of the TFGrid, directly integrated with your 3Bot. You only need JSON-RPC to communicate with your apps.
* Chat Bot Framework and DSL for all Clients in Vlang (25.000 github stars).
* Personal VPN service (WireGuard and Yggdrasil).
* Manage your 3Nodes, the nodes making up the TFGrid.
* Quantum Safe Storage System, WebDav, IPFS and more.

Planned Q4 2024

* Legalize all your digital assets, participate in security token offerings, co-own assets, escrow services, go from gold to digital and back easily. This will be an integrated feature of your 3Bot.
* Peer-to-Peer Exchange: exchange your assets peer-to-peer in a legal and efficient way.
* Disaster Recovery for your Private Keys, which is one unified way to safeguard your private keys.

Are you a developer?

* You will love to develop on top of a 3Bot. You can use any language to program against a 3Bot.
* Your developed applications will be made available to the 3Bot Community.
* We even have our own language called Vlang, which is like a simple and more efficient version of Go. You can extend the 3Bot using Vlang. We have DSL (Domain Specific Language) features for all our interfaces in Vlang.

Are you security paranoid?

* Great! We are too. We believe that the current decentralized world is not decentralized enough. Luckily, thanks to the ThreeFold Decentralized grid, you can deploy your Web3 Workloads in the most decentralized way possible.

Any Digital Experience can be built on top of a 3Bot, and the 3Bot can communicate with many Web2 or Web3 protocols.

![new-internet](./img/new-internet-14.png)

## Connection to Web3 World

We believe that a decentralized Web3 world should be supported by a decentralized Internet infrastructure, which is currently not the reality. Our aim is to build a robust, sustainable, and genuinely decentralized Internet capacity layer, including storage and computing resources, for any blockchain project to thrive on. Users of the ThreeFold Grid can quickly set up a blockchain node or development environment, typically within minutes. Our Quantum Safe Storage Layer provides a platform for the blockchain community to develop inventive solutions.

## This New Internet is Fully Compatible with the Existing One

Developers can use standard interfaces for system administrators like Docker and Kubernetes to deploy any solution possible, or to develop on top of a web interface (Open RPC) towards a 3Bot.

![new-internet](./img/new-internet-15.png)

More or less any application which runs in centralized data centers can be migrated to run on our decentralized Internet. As long as the application can run on Linux, it can run on the ThreeFold Grid.

For projects which are ready to step in the future, they can modify their web application to use the 3Bot backend, which provides huge benefits to do with security, energy efficiency, sovereignty, and also ease of use. The current Internet requires us to be experts in how we need to use all these different apps, it is just not logical.

## The Layer Zero for a More Decentralized World

We propose a feature-complete Internet and money system.

ThreeFold has successfully developed a peer-to-peer cloud operating system running on bare hardware (directly using the computer native resources) to make all of the above possible.

This is the result of 20 years of work.

![new-internet](./img/new-internet-16.png)

Imagine the capabilities and potential of a combination of blockchain and our technology to deliver a feature complete Internet and money system.
