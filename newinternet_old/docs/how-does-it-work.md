---
title: How Does it Work?
sidebar_position: 6
---


![new-internet](./img/new-internet-17.png)

## Become a ThreeFold Farmer

![new-internet](./img/new-internet-18.png)

Today you can become a farmer on our global proof of concept TFGrid network (version 3.0) see https://threefold.io/farm/, about 60,000 virtual CPU cores and 25,000,000 GB of storage are active.

We are launching our version 4.0 this summer which introduces our Internet of Internets concept. Each community can now roll out their own regional Internet, this allows us to establish a global interconnected network of regional or community Internets. Each community Internet has its own currency which can be exchanged to the global reserve currency. The global currency is used to allow people to go in between Regional Internets and execute on the Initial Staking requirement of a Regional Internet Validators.

## Comparing ThreeFold Farming to Electricity Generation

![new-internet](./img/new-internet-19.png)

* The token is the Internet Energy Token which represents Internet capacity (Storage, Compute, Network)
A token can be thought of like kWh in electricity generation. Tokens are generated by ThreeFold Farmers.

||Energy Solar Panel Production|TF Internet Capacity Production|
| --- | --- | --- |
|Unit of generation|= kWh|Unit of generation = tokens|
|Ownership Proof|= shares in special purpose vehicle|= blockchain smart contract|
|Liquidity|Low requires exit, or IPO|Higher, blockchain ecosystem|
|Security|Backed by all assets.|Backed by all assets.|
|Utility|Electricity Energy|Internet capacity energy. The fastest growing commodity in the world.|
|Expected ROI (per year)|5-12% IRR|25-44% IRR|

## Real Estate Development Project and ThreeFold

It makes a lot of sense to integrate into a real estate development project. Here’s why.

Imagine now a situation where people don’t have to do this by themselves, but where they can buy a house in which Internet Capacity is pre-integrated. Now their house is a node in a distributed and decentralized data center, creating benefits for the real estate developer as well as the homeowner. It can be compared to what a solar panel would do. Just like a solar panel generates electricity for the people around it, in the same way, our 3Nodes provide compute, storage and other Internet services to the people and companies nearby. A company or government will be able to host their data center Internet applications in the distributed data center which is part of the real estate project, which delivers greater security and performance at a better cost.

We call this: a “ThreeFold Neighborhood Cloud.”

## Benefits of the Neighborhood Cloud Concept

There are clear benefits to our neighborhood cloud concept, not only for the homeowners and the developers, but also for countries as well as the planet. This neighborhood cloud concept is in effect a decentralized data center. Let’s see some of its benefits.

Benefits for the Planet

* By enabling home owners to participate in a shared Internet economy, we avoid having to build power consuming data centers. Our 3Bot technology consumes only 2-5 watt per person, which is nothing compared to the Internet today.
* We involve our sustainability partners in the real estate project to have an impact on the regenerative nature of a project.
* We impact at least 9 of the 12 Social Development Goals (SDGs) with our approach.

Benefits for the Country

* When a country gets more Internet capacity locally, fewer data centers need to be built.
* Data & applications remain in the country and this leads to more sovereignty.
* A local Internet has higher performance, with faster speeds, lower latency, and easier scalability.
* Green, a ThreeFold Neighborhood Cloud, is carbon negative.
* It provides more security, e.g.recently in Ukraine, data centers were the first target. This approach eliminates this risk.

Benefits for the Homeowner

* Freedom: there is no longer the requirement to be a product from the Facebooks of the world. You can own your data.
* Free access to a co-owned Internet with all benefits possible.
* It is like having access to a super computer which provides an immersive gaming and metaverse experience that can be rendered in the cloud at high quality. This leads to incredible possibilities. The kids for sure will love it. The technical ones can even develop on the local cloud.
* Have access to community services, e-commerce, local services, collaboration tools, communication tools, etc.
* As delegated farmers, they will earn income (see the simulation table below) and, with some luck, could create a significant passive income source, offsetting the home cost.
* More security, reliability, privacy and performance for their Internet experience.

Benefits for the Developer

* Faster & easier sales of the homes – in our first project, the developer sold a lot faster.
Within 2 months this developer asked us to do a 2nd project with double the amount of homes.
* Income for delivering Internet services to the companies, country and people around your property.
* More financial income, because the developer becomes co-owner of the local Internet and gets a percentage of the tokens proceedings. This can be quite substantial.
