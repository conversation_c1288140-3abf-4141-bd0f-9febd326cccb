---
title: FAQ
sidebar_position: 8
slug: /faq
---

# Frequently Asked Questions

## How does the new internet work in remote areas with limited internet access?

While nodes do require an internet connection to communicate with the broader network, the system is designed to work in areas with limited or unreliable connectivity. Here's how it works in remote places:

1. **Low-Bandwidth Options**: Nodes can operate with lower bandwidth compared to traditional cloud services, allowing them to function even with weaker internet connections.

2. **Off-Grid Solutions**: In remote areas, the distributed infrastructure supports off-grid solutions. Nodes can be powered by solar energy, and with satellite or long-range wireless internet, they can still connect to the network.

3. **Secure Network Layer**: The decentralized secure network ensures that if one node has a weak connection, traffic can be automatically rerouted through other available nodes, maintaining secure and uninterrupted communication.

These features allow the new internet to be more resilient and accessible, even in areas with limited or no stable internet access, helping to make the internet more inclusive for everyone.

## How does the new internet help people in countries where the internet is censored or unreliable?

The new internet is a decentralized network, which means no single entity or government controls it. This makes it an effective option for people in countries where the internet is heavily regulated or censored. With this distributed infrastructure, individuals can have secure access to uncensored information, communicate freely, and access services without the fear of being monitored or blocked. By participating in the new internet, you're helping create a more open and resilient internet for everyone, no matter where they are.

## What are the rewards for participating in the network?

When you contribute your computer's unused capacity to the network by hosting a node, you earn incentive points. These points can be used to access services within the ecosystem, such as cloud storage, computing power, and more. The more you contribute to the network, the more points you earn. It's a way of being part of the community and supporting the infrastructure, while benefiting from the ecosystem you're helping to build.

## Can I use the new internet for business purposes?

Definitely! The new internet is not just for individuals. It can be a game-changer for businesses looking to reduce reliance on centralized cloud services. You can host your website, store data, and run AI models, all while benefiting from the decentralized and secure nature of the network. Whether you're a small business or a large organization, the new internet offers a private, reliable, and cost-effective alternative to traditional internet services.

## Why does the internet need to change? Doesn't everything work fine now?

The current internet is controlled by just a few big companies, which means they control a lot of what we see, do, and even think online. This creates issues like lack of privacy, data breaches, censorship, and limited access for people in certain regions. With the new internet, the goal is to make the digital world more fair, private, and accessible. By creating a decentralized internet, we can give people more control over their digital lives, help close the gap for people in underserved areas, and make the internet safer and more resilient for everyone. It's not about "fixing" the internet – it's about building something new and better that works for all of us.

## How is the new internet different from regular internet services like Google or Amazon?

Traditional internet services rely on centralized servers controlled by big companies like Google, Amazon, and others. These companies own and control your data, and they can decide what you can or can't access. In contrast, the new internet is a decentralized network, which means no single company owns it. Everyone can participate, whether by using the services or contributing computing power. This makes the new internet more secure, private, and resilient. Your data stays under your control, and the network is more democratic and transparent.

## What is a node and how do I set one up?

A node is simply a computer that contributes resources (computing power, storage, and bandwidth) to the decentralized network. The combined resources of all nodes form the infrastructure of the new internet. Setting up a node is straightforward:

1. Get compatible hardware – this can be a regular computer, server, or even a mini PC
2. Connect it to power and internet
3. Install the self-healing operating system

Once online, the node automatically joins the network and begins earning incentive points based on the resources it provides. The system is designed to be user-friendly and requires minimal maintenance due to its self-healing architecture.

## How secure is the new internet?

The new internet has been built with security as a fundamental principle, not an afterthought. Every connection is end-to-end encrypted by default. Data is distributed across multiple nodes, eliminating single points of failure. The architecture uses zero-trust principles, meaning nothing is trusted by default and verification is required for all access. Additionally, because the infrastructure is distributed rather than centralized, it's inherently more resistant to attacks, censorship, and surveillance. Your digital identity and data remain under your control at all times.

## How can I participate if I don't have technical knowledge?

The new internet is designed to be accessible to everyone, regardless of technical expertise. There are multiple ways to participate:

1. **As a user**: Simply use the Web4 products like decentralized websites, secure communication tools, and distributed storage
2. **As an infrastructure contributor**: Set up a node with a simple plug-and-play process that requires minimal technical knowledge
3. **As a community member**: Spread the word, educate others, or contribute in non-technical ways

The user interfaces are designed to be intuitive, and comprehensive documentation is available for those who want to learn more. Remember, the goal is to make the internet more inclusive, not more complicated.

## Will the new internet work with my existing devices and applications?

Yes! The new internet is designed to be compatible with existing devices and applications. You can access Web4 services from your current smartphones, laptops, and desktops. Many applications can run on the decentralized cloud without modification. Additionally, bridges exist to connect the traditional internet with the new infrastructure, allowing for a gradual transition rather than requiring an immediate switch. As more native applications are developed specifically for the new internet, the experience will become even more seamless and powerful.

## Is my data really private on the new internet?

Yes, your data is truly private on the new internet by design. Unlike the current internet where your data is harvested, analyzed, and monetized by large corporations, the new internet puts you in control. Your data is stored on encrypted, distributed systems that you authorize. No central authority can access your information without your explicit permission. You decide what to share, with whom, and for how long – and you can revoke access at any time. This isn't just a promise or policy that could change; it's built into the fundamental architecture of the system.
