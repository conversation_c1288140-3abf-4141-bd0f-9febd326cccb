---
title: How It Works
sidebar_position: 2
---

# The Foundation of a New Internet

## Building the Internet Layer by Layer

The new internet is built on a foundation of distributed infrastructure, owned and operated by people rather than corporations. This layered approach creates resilience, privacy, and autonomy at every level.

## Infrastructure Contributors

Infrastructure contribution is a unique concept that empowers individuals and organizations to participate directly in the internet's foundation.

As an infrastructure contributor, you can: 

- Deploy nodes that connect to the distributed computing grid
- Contribute computing, storage, and network resources
- Earn incentive points for providing capacity to the network
- Earn additional points when your capacity is actively used

## A Contributor's Journey

Infrastructure contributors are individuals and organizations who operate nodes that provide capacity to the distributed network. These nodes use standard computer hardware running a self-healing, zero-trust operating system that operates completely autonomously. Contributors simply need to provide electricity and internet connectivity—then the system takes care of itself.

Unlike traditional servers that require constant maintenance, updates, and administration, these nodes automatically repair themselves, apply updates, and optimize performance without human intervention. This self-healing architecture means you don't need to be a technical expert to participate in building the new internet. The system is designed to run continuously with minimal oversight, though contributors may occasionally need to replace faulty hardware components.

## How the Incentive System Works

When you contribute resources to the distributed grid by running a node, you earn incentive points, similar to how you earn miles on a flight or points with your credit card.

Every time your node is online and providing capacity (compute, storage, network), the system recognizes your contributions and allocates points. These points measure and acknowledge the value you add to the network.

You earn incentives in two main ways:

### Base Allocation
As long as your node is powered on and connected, you earn points for the resources you're making available to others, even when they're not being actively used. This is similar to earning points simply for maintaining a credit card, even if you don't use it daily.

### Usage-Based Allocation
When other people use the storage or compute capacity your node provides, you earn additional points based on actual usage, similar to how you earn more miles when you fly with an airline.

### Geographic Distribution Bonus
If your node is deployed in an underrepresented region or an area that lacks proper infrastructure, you'll earn additional points. The longer your nodes stay online and active, the more points you accumulate.

## The Circular Economy

The incentive system creates a powerful economic model where value continuously flows between participants in the network. Points earned through infrastructure contribution directly enable participation in the ecosystem.

With these points, you can interact with the entire ecosystem by:
- Deploying your own applications and workloads
- Storing your data securely and privately
- Running websites and online services
- Utilizing AI capabilities and computing resources
- Accessing premium network features

The more points you accumulate, the more capacity you can utilize across the network. This creates a circular economy where contributors are also users, and users can become contributors, aligning incentives across the entire ecosystem.

## Why Infrastructure Contribution Matters

Contributors form the backbone of the distributed grid. Without them, this new internet wouldn't exist.

By contributing infrastructure, you're strengthening a decentralized, resilient internet that belongs to everyone, and you receive fair compensation for doing so.

Whether you're an individual contributor or an organization running multiple nodes, your participation makes a tangible difference in creating a more equitable, privacy-respecting, and user-owned internet.

## Getting Started

Becoming an infrastructure contributor requires only a few essentials:

- Standard computer hardware (new or repurposed)
- Reliable internet connection
- Stable power supply

The system is designed to work with minimal technical knowledge, making it accessible to nearly anyone who wants to participate in building the internet's foundation.