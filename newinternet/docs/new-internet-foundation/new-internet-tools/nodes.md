---
title: Nodes
sidebar_position: 2
---

# Nodes

Nodes are small powerful computers that contribute to a decentralized, open-source internet infrastructure. Instead of relying on big corporations to host websites, applications, and data, these nodes work together to form a global, community-powered network.

## How It Works

Setting up a node is simple. All you need is a computer, electricity, and an internet connection. Once online, your device becomes part of the distributed computing grid, helping power decentralized services across the world.

In return for sharing your resources, you earn tokens based on the computing capacity you provide and their usage by the network. This creates a circular economy where anyone can both contribute to and benefit from the network.

## Key Benefits

- **Decentralization**: Reduces reliance on corporate data centers
- **Resilience**: No single point of failure means higher reliability
- **Sovereignty**: Communities can own their own computing infrastructure
- **Sustainability**: Distributed computing can be more energy-efficient than massive data centers
- **Economic**: Earn rewards by sharing your computing power with the network

## Use Cases

Nodes can support a wide range of applications:

- Web hosting
- Application deployment
- Database services
- Blockchain nodes
- Development environments
- AI and machine learning
- Data storage and backup

## Getting Started

Anyone can set up a node and start contributing to the decentralized internet. The process is straightforward and well-documented, making it accessible to both technical and non-technical users.

By running a node, you're directly helping to build a more equitable, resilient, and user-owned internet infrastructure.
