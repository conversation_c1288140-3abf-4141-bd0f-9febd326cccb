---
title: Decentralized Cloud
sidebar_position: 7
---

# Decentralized Cloud

The Decentralized Cloud is an open-source and distributed cloud platform that allows anyone to use, host, and contribute computing power to create a more secure, private, and efficient internet. Unlike traditional cloud service providers, this cloud infrastructure is built on a global network of independent computers, which means it's not owned by any single company.

## How It Works

The Decentralized Cloud operates on a network of distributed nodes contributed by individuals and organizations around the world. These resources are pooled together, creating a global compute, storage, and network infrastructure that anyone can use.

This platform can run anything that works on Linux, one of the most widely used operating systems. It can support websites, applications, AI workloads, and blockchain technologies with the same flexibility as traditional cloud platforms but with enhanced privacy and resilience.

## Key Benefits

- **True Sovereignty**: Your data and applications remain under your control
- **Privacy by Design**: No central authority can access your data
- **Cost Effective**: Often more affordable than traditional cloud services
- **Resilient Infrastructure**: Distributed architecture means no single point of failure
- **Sustainable**: Utilizes existing hardware resources more efficiently
- **Open Source**: Transparent code that anyone can inspect or contribute to

## Core Services

The Decentralized Cloud offers all the essential services you'd expect from a modern cloud platform:

- **Compute**: Virtual machines, containers, and serverless functions
- **Storage**: Object storage, file systems, and databases
- **Networking**: Virtual networks, DNS, and load balancing
- **Web Hosting**: Deploy static and dynamic websites
- **Application Platform**: Deploy and scale applications
- **Kubernetes**: Managed container orchestration
- **Development Tools**: CI/CD pipelines and development environments

## Use Cases

- **Web Application Hosting**: Deploy websites and web applications
- **Business Applications**: Run enterprise software securely
- **Development & Testing**: Scalable environments for software development
- **Data Storage & Backup**: Secure, distributed storage for important files
- **AI & Machine Learning**: Deploy models in a privacy-respecting way
- **IoT Backend**: Secure infrastructure for connected devices
- **Private Networking**: Create secure overlay networks for your organization

## Getting Started

Using the Decentralized Cloud is similar to using traditional cloud services, with straightforward interfaces for deploying and managing resources. The main difference is that you're leveraging a distributed network rather than a centralized provider.

For those who want to contribute resources rather than consume them, the process is as simple as setting up a Compute Node and connecting it to the network.

By supporting and using the Decentralized Cloud, you're helping build a more resilient, private, and equitable internet infrastructure that serves people rather than corporate interests.
