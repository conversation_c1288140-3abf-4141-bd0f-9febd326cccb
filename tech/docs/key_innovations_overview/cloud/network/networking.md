---
sidebar_position: 1
title: 'Technical Overview'
---


# Network Architecture Overview

The distributed networking platform implements connectivity for compute and storage workloads through private overlay networks with secure public Internet exposure capabilities. This peer-to-peer architecture enables workload connectivity over encrypted channels that dynamically identify optimal routing paths between nodes.

### Secure Mesh Overlay Implementation (Peer-to-Peer)

ZNet forms the foundational layer for distributed grid architectures. It functions as a virtual private data center, enabling full mesh connectivity where each of *N* containers can establish secure connections with all *(N-1)* other containers in the network. This creates a comprehensive peer-to-peer network topology between containerized workloads.

By design, the network maintains isolation from public Internet infrastructure. ZNet implements a single-tenant model with no default public Internet connectivity, ensuring all communications remain within the private network boundary. For scenarios requiring public access, the Web Gateway component provides controlled Internet exposure when explicitly configured.

### Redundancy Architecture

The Web Gateway integration implements several redundancy mechanisms:

- Applications can establish secure Internet connectivity through any available IP address provided by network infrastructure operators
- Multi-gateway connectivity allows applications to utilize multiple web gateways simultaneously, implementing DNS round-robin for load distribution and connection redundancy
- The clustering architecture enables continuous service availability despite gateway or node failures
- Simplified maintenance operations: When containers are relocated or recreated, existing end-user connections remain valid as they terminate at the Web Gateway layer. Relocated containers establish new socket connections to the Web Gateway to resume traffic processing.

### Network Security Boundary

For specialized OEM implementations, the architecture supports deployment models that operate without TCP/IP or Ethernet protocols, creating highly secure environments optimized for high-security applications and cyber threat mitigation.
