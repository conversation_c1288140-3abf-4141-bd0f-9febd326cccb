---
title: Shortest Path Routing
sidebar_position: 5
---


# Shortest Path Routing

## Technical Implementation of End-to-End Encrypted Overlay Network

### End-to-End Encryption Architecture

End-to-end encryption (E2EE) implements a security model where data is encrypted on the originating device and only decrypted at the destination device. This architecture ensures that no intermediary nodes, including service providers, can access or modify the data during transmission.

### Shortest Path Routing Algorithm Implementation

An overlay network creates a virtual network topology on top of existing physical network infrastructure.

Each endpoint Mycelium agent executes custom routing logic and protocols to optimize connectivity parameters.

- In the Mycelium peer-to-peer (P2P) overlay implementation, nodes dynamically discover and establish connections with other participants, creating a mesh network topology.
- The routing algorithm calculates the shortest or most efficient path between nodes based on multiple parameters including latency, bandwidth, and reliability metrics. This optimization reduces transmission latency and improves overall network performance.

### Multi-Hop Transmission Protocol

The P2P overlay architecture enables data transmission through multiple intermediate nodes when direct connections are unavailable. The implementation works as follows:

1. When **Node A** initiates data transmission to **Node D**
2. If no direct connection exists, the routing algorithm identifies a path through intermediate nodes: **Node A** → **Node B** → **Node C** → **Node D**
3. The E2EE protocol ensures data remains encrypted throughout the entire transmission path

The implementation includes network usage tracking and compensation mechanisms to ensure equitable resource allocation among participating nodes.

### Existing Infrastructure Integration

The overlay network architecture operates as a protocol layer on top of existing internet infrastructure.

This implementation approach provides:

1. **Resource Optimization**: Leveraging existing infrastructure eliminates redundant hardware investments
2. **Adaptive Routing**: The network dynamically reconfigures in response to changing conditions such as congestion or node failures

### Connectivity Enhancement for Limited-Access Regions

Approximately 4 billion people currently lack reliable internet access.

The Mycelium architecture addresses this limitation through:

1. **Distributed Access Points**: Users in remote or underserved regions can connect through nearby nodes operated by community members or commercial bandwidth providers
2. **Community Network Implementation**: Local node clusters can establish connections to the broader overlay network, creating resilient regional connectivity
3. **Bandwidth Aggregation**: The architecture combines available bandwidth from multiple sources to provide improved data rates and connection reliability

### Technical Implementation Scenario

Consider a remote location with limited connectivity infrastructure. Residents implement multiple nodes that establish connections with each other and with nodes in areas having better connectivity. Some nodes connect to the internet via satellite, cellular, or other available transmission methods.

The technical implementation functions as follows:

1. **Local Mesh Deployment**: Residents deploy nodes on available devices, establishing a local mesh network
2. **Uplink Diversity**: Selected nodes implement connections to external networks via satellite, long-range wireless, or other available transmission methods
3. **Dynamic Path Calculation**: When a user initiates a connection, the data is encrypted end-to-end and routed through the optimal available path, which may include local nodes, satellite links, and traditional internet infrastructure
4. **Bandwidth Optimization**: This architecture leverages all available transmission methods, providing improved reliability and performance compared to single-connection approaches
