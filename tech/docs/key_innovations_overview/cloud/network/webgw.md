---
sidebar_position: 3
title: Web Gateway Architecture
---

# Web Gateway Architecture

The Web Gateway implements a network architecture that connects private networks to the public Internet while maintaining isolation between Internet-facing services and secure workloads running in Zero VMs.

### Technical Advantages

- Architectural separation between compute workload execution and service exposure points
- High availability through multi-gateway redundancy for each application
- Multi-protocol interface support
- IPv4 address conservation through efficient address utilization

### Technical Implementation

Nodes with gateway capability (configured by infrastructure providers) can accept gateway workloads and implement traffic forwarding to Zero VMs that operate exclusively with Planetary Network or IPv6 addressing.

Gateway workloads require a namespace reservation (prefix) on the distributed ledger, followed by backend IP configuration. Additional parameters control TLS implementation and other connection properties (detailed configuration parameters are available in the Terraform documentation).

Upon workload assignment, the node configures a proxy service for the reserved namespace and associated Planetary Network endpoints.

### Security Architecture

Zero VMs require Planetary Network IP or IPv6 addressing (IPv4 is also supported). This architecture means that any entity connected to the Planetary Network can directly access the Zero VM without proxy mediation.

Security responsibility remains with the Zero VM administrator to implement appropriate access controls and port restrictions.

### Redundant Network Implementation

![](../../../img/redundant_net.jpg)

### Horizontal Scalability

![](../../../img/webgw_scaling.jpg)

The network architecture implements a horizontally scalable system with no inherent bottlenecks. Network capacity (supply) is provided by infrastructure operators, while network utilization (demand) is generated by grid users.

Supply and demand scale independently in this architecture. On the supply side, the system supports unlimited infrastructure providers offering web gateway services on their nodes, alongside compute and storage resources. The demand side is driven by application developers, system integrators, and enterprise solutions. Global demand for data processing and storage continues to grow exponentially across diverse use cases.
