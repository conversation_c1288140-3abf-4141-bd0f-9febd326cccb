---
sidebar_position: 4
title: Zero Knowledge Proof
---

# Zero Knowledge Proof Storage Architecture

The Quantum Safe Storage System implements zero knowledge proof principles in its architectural design. The system architecture consists of two distinct components: the physical storage nodes (ZDBs) that store data fragments and the ZStor (Zero Stor) Component that manages the distribution and retrieval processes.

![Diagram](https://kroki.io/plantuml/svg/eNqVVMtu2zAQvPMrtukhhzaH5gEERhHkZZ_aGK1yyiWgSVoiTHEFcgVBKPLvXcov2bIK9GYtZ2Zndxb-RIUpDSxcbfIgW3EfSQaqSyfiyvpKBlnCQqpVHrD2-gkdBqAgfeQn46mHUlhW6LmWUesMBKNI-tyZHiQWUmNjfQ5L6WL_5SEEbNbqn29vb0_Jwh8B8IhBm9ADcunIXVNYMuKjJ7Gz8n8SADP01AN-CEFYASEskAhL0DZJW_SisNqAKStqQZuogq26sjiHH6ikA96qWk3gVzbL4Au8Za_z32Jv62wWuJPxGt7mGbygNmed1f3siXhQ6CTep14xOJx4-WlIakkyme66XlzcHZJgAk3gOaGDHT7twVsdRkfCwEMmSIrQ-iUK4ZEVNuvA5cBWVjlLsWvBBN4c00NbkdFfORbNG5dseRlkXrL3KNIOkuSI8G4qgLl3LVBhI-N59yuPTYQCmxQPbxZ9pFArAgw2t54j6Hh7_fMu9fSd5pK5ASdb9txL5dmyhl3U7LYLZktIAcXjhBjw_m1QuRxUrgaV60HlJqV2IpHUgoP4rtJNTtJN3m1XBw8jhMsxwuMI4WqM8DRCuB4jPI8QbsYIUzHMfbPWqVTFOunuDCNgyl_uTucfl7OJ4QXXfCU9H8iitk7z_Zjju7jnn-nv7y9RxrYD)

<!--
!theme bluegray
@startuml
skinparam backgroundColor transparent
skinparam componentStyle rectangle
skinparam shadowing false
skinparam ArrowColor #888
skinparam component {
  BorderColor #888
  BackgroundColor white
}
skinparam rectangle {
  BorderColor #888
  BackgroundColor white
  FontColor #888
}

top to bottom direction
hide empty description

' Local stack: QSFS + ZSTOR
rectangle "Frontend ZOS Node" {
  component QSFS
  component ZSTOR_Encoder
  component ZSTOR_Metadata
}

QSFS \-\-\> ZSTOR_Encoder : write data
ZSTOR_Encoder \-\-\> ZSTOR_Metadata : store encoding info

note bottom of ZSTOR_Encoder
  Splits data into encrypted, redundant fragments
end note

note bottom of ZSTOR_Metadata
  Only this node knows how to reconstruct original data
end note

' Backend storage layer
rectangle "Distributed ZOS Backend Nodes" {
  component ZOS_1
  component ZOS_2
  component ZOS_3
  component ZOS_4
  component ZOS_5
}

ZSTOR_Encoder \-\-\> ZOS_1 : <color:#888>fragment A
ZSTOR_Encoder \-\-\> ZOS_2 : <color:#888>fragment B
ZSTOR_Encoder \-\-\> ZOS_3 : <color:#888>fragment C
ZSTOR_Encoder \-\-\> ZOS_4 : <color:#888>fragment D
ZSTOR_Encoder \-\-\> ZOS_5 : <color:#888>fragment E


note bottom of ZOS_1
  Each node stores only a fragment
end note

note bottom of ZOS_2
  No node can rebuild the data
end note

@enduml

-->
<!-- 
[Edit this diagram](https://niolesk.top/#https://kroki.io/plantuml/svg/eNqVVMtu2zAQvPMrtukhhzaH5gEERhHkZZ_aGK1yyiWgSVoiTHEFcgVBKPLvXcov2bIK9GYtZ2Zndxb-RIUpDSxcbfIgW3EfSQaqSyfiyvpKBlnCQqpVHrD2-gkdBqAgfeQn46mHUlhW6LmWUesMBKNI-tyZHiQWUmNjfQ5L6WL_5SEEbNbqn29vb0_Jwh8B8IhBm9ADcunIXVNYMuKjJ7Gz8n8SADP01AN-CEFYASEskAhL0DZJW_SisNqAKStqQZuogq26sjiHH6ikA96qWk3gVzbL4Au8Za_z32Jv62wWuJPxGt7mGbygNmed1f3siXhQ6CTep14xOJx4-WlIakkyme66XlzcHZJgAk3gOaGDHT7twVsdRkfCwEMmSIrQ-iUK4ZEVNuvA5cBWVjlLsWvBBN4c00NbkdFfORbNG5dseRlkXrL3KNIOkuSI8G4qgLl3LVBhI-N59yuPTYQCmxQPbxZ9pFArAgw2t54j6Hh7_fMu9fSd5pK5ASdb9txL5dmyhl3U7LYLZktIAcXjhBjw_m1QuRxUrgaV60HlJqV2IpHUgoP4rtJNTtJN3m1XBw8jhMsxwuMI4WqM8DRCuB4jPI8QbsYIUzHMfbPWqVTFOunuDCNgyl_uTucfl7OJ4QXXfCU9H8iitk7z_Zjju7jnn-nv7y9RxrYD) -->


## Technical Implementation

The zero knowledge proof compliance is achieved through a verification mechanism where physical storage nodes (3Nodes) can cryptographically prove they store valid data fragments without having access to or knowledge of the complete data set. 

The ZStor can validate the integrity of all storage devices by verifying they maintain valid fragments of the original information. This verification occurs without requiring the complete dataset to be reconstructed.

The storage nodes operate with zero knowledge of the original data content, as each node only contains mathematical representations (partial descriptions) of the original data. These fragments are mathematically insufficient to reconstruct the original data objects without access to multiple other fragments stored on independent nodes.

This architecture implements data privacy by design, as no single storage component has sufficient information to determine the nature or content of the stored data.
