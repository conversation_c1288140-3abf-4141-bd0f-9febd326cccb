---
sidebar_position: 1
---

# Unbreakable Storage Architecture

The distributed storage architecture implements a true peer-to-peer design principle within the decentralized cloud infrastructure.

![](img/unbreakable_storage.png)

Each participating node stores only small, incomplete fragments of data objects (files, photos, videos, databases, etc.) by allocating a portion of its local storage resources. The management of fragment distribution, storage, and retrieval is handled by a specialized software layer that provides development and end-user interfaces to the underlying storage algorithm. This approach is referred to as '**dispersed storage**'.

## Technical Advantages

- Linear scalability from petabyte to zetabyte capacity
- Self-healing architecture with autonomous operation
- Automatic detection and correction of data degradation (bitrot)
- Granular geographic control over data placement for regulatory compliance (e.g. GDPR)

## Distributed Architecture Benefits

The peer-to-peer architecture enables precise selection of storage providers based on specific application requirements and business criteria. For example, organizations can implement geographic data placement policies to address governance and compliance requirements. Different storage policies can be applied to different data categories, such as distinguishing between active and archived data.

These diverse use cases are supported by the distributed storage architecture, utilizing the same infrastructure components provided by storage node operators and consumed by developers or end-users.

> The architecture provides precise control over data placement while implementing advanced security through mathematical dispersion.

## Architecture

This architecture is based on a **scale-out model**. Each storage use case operates with its own instance of **ZSTOR** and **QSFS**, ensuring scalability, performance isolation, and data privacy. Data is always processed and consumed locally, where it is produced.

### High Level

- QSFS is the Filesystem which connects to ZStor
- Each ZStor connects to + 20 ZDB's which can be chosen in any geographic location (on image we only draw few)
- There can be unlimited amounts of QSFS and ZSTOR's

![Diagram](https://kroki.io/plantuml/svg/eNqVlDFvwjAUhHf_ilc6tB0YMAxMFSSIsahNB_BSmcQkFokdOUYIVf3vdUJCDLaKukWfz3f3bCsPOmMFg21-YKmiJzSrNFX6UOSo2nNRUkUL2NJ4nyp5EEkoc6lAKyoqs8SEtlSxLEopDIv0KWegWKypSHNmSaqMJvLIRQo7mlf2ylwpeTy7P06nU-TzhW8EEEiVMGUpDbqpd8y4ZujH9riU-Z8HwFIKbQmNac52GrQExdNMQ8Jray4FynjCgBWlPkHCqljxssGojx7UIUwkQFYRvEkjnw-aPv2EZBF8zUcuwi4a12X-Mg985oFrHuB7TqHPKXSdQrdmOHbR5CZvqcwhXwWuncDoc_Xxtb6OfI-W0S1rhdgjxHdjN_7YjSfWsHtuxO9GPG7ENwRpCj81ivrBNRS1Q8Nw-NqfSjugDTFqe1pw0yqJDU22CWm-m5RFAM8FqzKWvKAu4Cw_v02HBReGex12mXkKqOvRb8YuC0cehrvNxGoz7piVMnGZCUEzczf1X-0XeNeKkQ==)

<!--
!theme bluegray
@startuml
skinparam backgroundColor transparent
skinparam componentStyle rectangle
skinparam shadowing false
skinparam ArrowColor #888

skinparam component {
  BorderColor #888
  BackgroundColor white
}

skinparam rectangle {
  BorderColor #888
  BackgroundColor white
  FontColor #888
}

left to right direction
hide empty description

rectangle "Backend ZOS Node A" {
  component ZDB_A1
  component ZDB_A2
  component ZDB_A3
}

rectangle "Backend ZOS Node B" {
  component ZDB_B1
  component ZDB_B2
}

rectangle "Backend ZOS Node C" {
  component ZDB_C1
  component ZDB_C2
  component ZDB_C3
  component ZDB_C4
}

rectangle "Frontend ZOS Node X" {
  component ZSTOR_X1
  component QSFS_X1
  component ZSTOR_X2
  component QSFS_X2
}

rectangle "Frontend ZOS Node Y" {
  component ZSTOR_Y1
  component QSFS_Y1
}

rectangle "Frontend ZOS Node Z" {
  component ZSTOR_Z1
  component QSFS_Z1
  component ZSTOR_Z2
}

' QSFS to ZSTOR
QSFS_X1 \-\-\> ZSTOR_X1
QSFS_X2 \-\-\> ZSTOR_X2
QSFS_Y1 \-\-\> ZSTOR_Y1
QSFS_Z1 \-\-\> ZSTOR_Z1

' ZSTOR to ZDB (meshed)
ZSTOR_X1 \-\-\> ZDB_A1
ZSTOR_X1 \-\-\> ZDB_B1
ZSTOR_X2 \-\-\> ZDB_A2
ZSTOR_X2 \-\-\> ZDB_C3

ZSTOR_Y1 \-\-\> ZDB_B2
ZSTOR_Y1 \-\-\> ZDB_C1
ZSTOR_Y1 \-\-\> ZDB_C2

ZSTOR_Z1 \-\-\> ZDB_A3
ZSTOR_Z2 \-\-\> ZDB_C4
ZSTOR_Z2 \-\-\> ZDB_B2

@enduml

-->


### Components

![Diagram](https://kroki.io/plantuml/svg/eNp9U0Fu2zAQvOsVW_fYOMmlgE9FaqtGLqnRqL0k8GEtrmUiIlcg1wiEIn17l4qc0rDTmzgzmlktRx9kR45g0-6pCdgXN1EwyN61RXyyvsOADjZYPzWB994suOUAEtBHpchLpqrZdewVq6RvCQLVgr5pKZPEHRp-tr6BLbYxZ76GwM-v7h9ns1lxzhd-FwBzDoZCpnzJtW-h57UAS_Zy_HJLWwFhCLbZCRibLCz7YmcNAblOejAU62C7AS7-RUwq4YANwa9IsMBIE03VjEzxUP1c3U-GYQAehxPcsbf64hqm0-kBeyjn6_9oVlVSwFzvgbw5VpJgkn15zyqdc_6br1m3cqQ5YKPuNO4lfdfjj2pZncs6JYapirTefBmr6nUVJwmAMZ2VSujgc1uWcAVVVa5z9Ds60ubVFI_gEgVhqXfhtCZKaa5nofFOeTua35OxcZr6hGI3OpJBcuwvlUpXSVEBNdoejIB9218WOiEku9FUuEuWp-tcqLGaRIoXQL4OfSfxQnE0JgJpwYN2OYz9etd22GVqqm0p9lHI6RgHdshM8_75fP0J7uZXEToKsNcC1lrA3PRGH9Nv_BdLvzrb)

<!--
!theme bluegray
@startuml
skinparam backgroundColor transparent
skinparam componentStyle rectangle
skinparam shadowing false
skinparam ArrowColor #888

skinparam component {
  BorderColor #888
}

skinparam rectangle {
  BorderColor #888
  FontColor #888
}

left to right direction
hide empty description

rectangle "Storage Use Case" {

  rectangle "ZSTOR" {
    [ZSTOR Monitor] --- [ZSTOR ZDB]
    [ZSTOR Monitor] --- [ZOS ZDB Backend]
    [ZSTOR Meta] \-\-\> [ZSTOR ZDB]
    [ZSTOR ZDB] \-\-\> [ZSTOR Encoder]
    [ZSTOR Encoder] \-\-\> [ZOS ZDB Backend]
  }

  [QSFS] \-\-\> [ZSTOR ZDB]
  [QSFS] \-\-\> [ZSTOR Meta]

}

rectangle "ZOS" {
  [ZOS ZDB Backend] as ZDB
  ZDB \-\-\> [HDD / SSD]
  ZDB \-\-\> [Namespaces]
  ZDB \-\-\> [Data Fragments]
}

note right of ZDB
  Redis-compatible daemon.
  Stores data fragments only.
end note

note top of [ZSTOR Encoder]
  Compresses, encrypts,
  adds error correction.
end note

note top of [QSFS]
  Filesystem on top of ZSTOR.
  ~50+ MB/s per use case.
end note

@enduml

-->

#### ZOS ZDB (Zero-DB)
The **ZDB** is the storage backend. It is a Redis-compatible storage daemon that stores data fragments directly on HDD or SSD. It is part of the ZOS (Zero-OS) platform.

- Optimized for high-performance writes to SSD and HDD.
- Supports **namespaces**, which are uniquely reserved per user or system.
- Stores only **data fragments** without any context; these fragments are meaningless on their own. Only the **ZSTOR** component can interpret and reconstruct meaningful data.

#### ZSTOR
**ZSTOR** is responsible for encoding data, managing metadata, and coordinating storage operations.

- **ZSTOR Encoder**: Performs data compression, encryption, and applies forward error correction (FEC) to split data into resilient fragments.
- **ZSTOR ZDB**: A local staging area where incoming data is first aggregated for performance. At configurable intervals (based on time or volume), data is encoded and flushed to remote ZOS ZDB backends.
- **ZSTOR Monitor**: Exposes monitoring metrics for systems like Prometheus and provides health/status information for the storage cluster.
- **ZSTOR Meta**: Stores metadata (used, for example, by QSFS) within its own ZSTOR ZDB.
- **ZSTOR Cluster**: (OEM only) Enables real-time synchronization between multiple ZSTOR instances in **sync mode** to maintain redundancy or geographic failover.

#### QSFS (Quantum Safe File System)
**QSFS** is a distributed file system built on top of ZSTOR.

- Designed for secure and scalable storage, not for ultra-high performance.
- Achieves performance up to **50+ MB/sec per use case**, depending on configuration and storage hardware.
- Ideal for distributed workloads requiring data privacy and local processing.


The architecture is a scale out architecture, each storage usecase has its own ZSTOR & QSFS, this allows for good scalability and also privacy. Only where the data is produced/consumed data can be re-assembled this provides for very good security.

Remark a system like this is very good for file based systems, but not for databases.


<!-- 
----

[edit](https://niolesk.top/#https://kroki.io/plantuml/svg/eNp9U0Fu2zAQvOsVW_fYOMmlgE9FaqtGLqnRqL0k8GEtrmUiIlcg1wiEIn17l4qc0rDTmzgzmlktRx9kR45g0-6pCdgXN1EwyN61RXyyvsOADjZYPzWB994suOUAEtBHpchLpqrZdewVq6RvCQLVgr5pKZPEHRp-tr6BLbYxZ76GwM-v7h9ns1lxzhd-FwBzDoZCpnzJtW-h57UAS_Zy_HJLWwFhCLbZCRibLCz7YmcNAblOejAU62C7AS7-RUwq4YANwa9IsMBIE03VjEzxUP1c3U-GYQAehxPcsbf64hqm0-kBeyjn6_9oVlVSwFzvgbw5VpJgkn15zyqdc_6br1m3cqQ5YKPuNO4lfdfjj2pZncs6JYapirTefBmr6nUVJwmAMZ2VSujgc1uWcAVVVa5z9Ds60ubVFI_gEgVhqXfhtCZKaa5nofFOeTua35OxcZr6hGI3OpJBcuwvlUpXSVEBNdoejIB9218WOiEku9FUuEuWp-tcqLGaRIoXQL4OfSfxQnE0JgJpwYN2OYz9etd22GVqqm0p9lHI6RgHdshM8_75fP0J7uZXEToKsNcC1lrA3PRGH9Nv_BdLvzrb)

[edit](https://niolesk.top/#https://kroki.io/plantuml/svg/eNqVlDFvwjAUhHf_ilc6tB0YMAxMFSSIsahNB_BSmcQkFokdOUYIVf3vdUJCDLaKukWfz3f3bCsPOmMFg21-YKmiJzSrNFX6UOSo2nNRUkUL2NJ4nyp5EEkoc6lAKyoqs8SEtlSxLEopDIv0KWegWKypSHNmSaqMJvLIRQo7mlf2ylwpeTy7P06nU-TzhW8EEEiVMGUpDbqpd8y4ZujH9riU-Z8HwFIKbQmNac52GrQExdNMQ8Jray4FynjCgBWlPkHCqljxssGojx7UIUwkQFYRvEkjnw-aPv2EZBF8zUcuwi4a12X-Mg985oFrHuB7TqHPKXSdQrdmOHbR5CZvqcwhXwWuncDoc_Xxtb6OfI-W0S1rhdgjxHdjN_7YjSfWsHtuxO9GPG7ENwRpCj81ivrBNRS1Q8Nw-NqfSjugDTFqe1pw0yqJDU22CWm-m5RFAM8FqzKWvKAu4Cw_v02HBReGex12mXkKqOvRb8YuC0cehrvNxGoz7piVMnGZCUEzczf1X-0XeNeKkQ==) -->


## Known Issues

While the current implementation of our decentralized storage stack is technically robust and capable of powering real-world use cases, it remains accessible primarily to a highly technical audience. Users are required to understand lower-level components like QSFS, ZSTOR, and ZDBs, as well as how to manually configure and orchestrate them across distributed nodes.

This complexity makes the system unsuitable for adoption by a general audience at this stage. However, this is about to change with the introduction of FungiStor.

FungiStor is a next-generation abstraction layer that simplifies the entire experience. It brings a user-friendly architecture on top of the existing stack, with automated coordination, smarter defaults, and integrated content addressing. With FungiStor, developers and non-experts alike will be able to use decentralized storage without needing to understand the underlying infrastructure — making it as easy to use as cloud storage, but with the benefits of decentralization, privacy, and performance.

