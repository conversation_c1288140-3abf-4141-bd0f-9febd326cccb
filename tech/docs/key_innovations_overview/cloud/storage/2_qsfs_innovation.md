---
title: Quantum Safe File System
sidebar_position: 2
---

### Technical Challenge

There is a growing need for more accessible and user-friendly solutions to store and manage large volumes of data efficiently.

While Zero-Stor provides an effective solution for many storage challenges, its technical complexity creates barriers for typical developers and system administrators. QSFS was developed to address this accessibility gap while maintaining the security and reliability benefits.

### QSFS Technical Architecture

![Diagram](https://kroki.io/plantuml/svg/eNqVlD9v2zAQxXd-imsydKgzxH8Ao0OROI2nokGrTl4CijxJhCVSIE8whCLfvUcFThSJHjrq3t1P9x4pfaIKG4S87rD0shd3gaSnrqlFOBrbSi8byKU6lt51Vj-42nkgL21gCS2NupRrWme5llFfI3hUJG1Z46glVFK7k7ElFLIOY-Xee3d6pV9vt9sUFv4KgJ3zGv2okUuT7U6VIRQvI8TbKv-HANg7S6PGFyHItUAOckfkGtAmoo2zojIaAZuWetAYlDftUBafmaG6gBp-ZfsMOFx1FO_7XO09vwKthsNTBj-dxqthx3fTcexD4ZD9efr9_GgVN_uEcvi-e_7hlKzjusNLb26-TaY-PI30t1n4CnlXFCx-AVmWHkvJgQjriA_WlBWBK86r7U2NoQ-EDRj24gupEArOTLZtiGEFch5BS5IiOo2QKWpq6oFNeQxhAWiV71ta4KAtBrtRGaE45HiA5xQt9wU4GaqA_YRx2ueQ4XYac3R-ez8v7WKKKcIyRVjOCcuLhFWKsJoTVhcJ6xRhPSesLxI2KcJmTtgMBA46M01X812A4Tzil6xNIG_ybrjviYv1Gmxa4HDSwurSxPrSBC8txB3fgfjr-gdSCZZG)

<!--
!theme bluegray
@startuml
skinparam backgroundColor transparent
skinparam componentStyle rectangle
skinparam shadowing false
skinparam ArrowColor #888
skinparam component {
  BorderColor #888
  BackgroundColor white
}
skinparam rectangle {
  BorderColor #888
  BackgroundColor white
  FontColor #888
}

top to bottom direction
hide empty description

' Focused QSFS stack
rectangle "Frontend ZOS Node" {
  component QSFS
  component ZSTOR_Encoder
  component ZSTOR_ZDB_Local
}

QSFS \-\-\> ZSTOR_Encoder
ZSTOR_Encoder \-\-\> ZSTOR_ZDB_Local : buffer + aggregate

note right of QSFS
  Filesystem interface for apps to store data
end note

note right of ZSTOR_Encoder
  Compress, encrypt,encode, compress
end note


' Backend ZOS nodes with ZDBs
rectangle "ZOS Node 1" {
  component ZDB_1A
  component ZDB_1B
}

rectangle "ZOS Node 2" {
  component ZDB_2A
  component ZDB_2B
}

rectangle "ZOS Node 3" {
  component ZDB_3A
  component ZDB_3B
}

rectangle "ZOS Node 4" {
  component ZDB_4A
  component ZDB_4B
}

rectangle "ZOS Node 5" {
  component ZDB_5A
  component ZDB_5B
}


' Simulate encoding distribution
ZSTOR_Encoder \-\-\> ZDB_1A
ZSTOR_Encoder \-\-\> ZDB_2B
ZSTOR_Encoder \-\-\> ZDB_3A
ZSTOR_Encoder \-\-\> ZDB_4B
ZSTOR_Encoder \-\-\> ZDB_5A


@enduml

-->
<!-- 
[Edit this diagram](https://niolesk.top/#https://kroki.io/plantuml/svg/eNqVlD9v2zAQxXd-imsydKgzxH8Ao0OROI2nokGrTl4CijxJhCVSIE8whCLfvUcFThSJHjrq3t1P9x4pfaIKG4S87rD0shd3gaSnrqlFOBrbSi8byKU6lt51Vj-42nkgL21gCS2NupRrWme5llFfI3hUJG1Z46glVFK7k7ElFLIOY-Xee3d6pV9vt9sUFv4KgJ3zGv2okUuT7U6VIRQvI8TbKv-HANg7S6PGFyHItUAOckfkGtAmoo2zojIaAZuWetAYlDftUBafmaG6gBp-ZfsMOFx1FO_7XO09vwKthsNTBj-dxqthx3fTcexD4ZD9efr9_GgVN_uEcvi-e_7hlKzjusNLb26-TaY-PI30t1n4CnlXFCx-AVmWHkvJgQjriA_WlBWBK86r7U2NoQ-EDRj24gupEArOTLZtiGEFch5BS5IiOo2QKWpq6oFNeQxhAWiV71ta4KAtBrtRGaE45HiA5xQt9wU4GaqA_YRx2ueQ4XYac3R-ez8v7WKKKcIyRVjOCcuLhFWKsJoTVhcJ6xRhPSesLxI2KcJmTtgMBA46M01X812A4Tzil6xNIG_ybrjviYv1Gmxa4HDSwurSxPrSBC8txB3fgfjr-gdSCZZG) -->

QSFS implements a filesystem architecture that utilizes Zero-Stor as its backend storage mechanism. The implementation includes metadata protection mechanisms that inherit Zero-Stor's security properties while providing a simplified interface for developers and system administrators.

The filesystem deployment model maintains a single-location interface while distributing data across multiple geographic sites using zero-stor for enhanced reliability.

The architecture includes metadata redundancy with configurable consistency levels. The distributed state synchronization typically operates with a maximum lag of approximately 15 minutes.

This filesystem implementation supports mounting under various storage applications, including backup servers, file servers, and S3-compatible storage services, providing significant deployment flexibility.

### Technical Specifications

- Inherits Zero-Stor's security architecture, efficiency algorithms, and scalability properties
- Implements a standardized interface for integration with diverse application ecosystems
- Provides scalability to petabyte levels with defined operational parameters
- Delivers data transfer performance of up to 50 MB/sec, optimized for larger file operations
- Supports up to 2 million files per filesystem instance

## Technical Implementation Details

The distributed filesystem technology implements several key technical features:

- Petabyte-scale storage capacity
- Advanced security architecture:
  - Data fragmentation prevents any single storage provider from accessing complete data
  - Implements post-quantum cryptographic algorithms
- Data integrity protection:
  - Automatic detection and repair of data corruption (bitrot)
- Persistent storage with no automatic deletion mechanisms
- Geographic data distribution:
  - Maintains availability despite site failures
- Resource efficiency: approximately 10x more efficient than traditional cloud storage systems
- Universal mounting capability across operating systems and container platforms (OSX, Linux, Windows, Docker, Kubernetes)
- Workload compatibility with most data storage requirements (excluding high-performance database workloads)
- Self-healing architecture: automatically restores redundancy levels after node or disk failures
- Regulatory compliance support: encryption and data fragmentation assist with GDPR requirements
- Deployment flexibility: supports on-premises, public, and hybrid implementations
- Performance optimization through read-write caching on encoding nodes

### Technical Components

The QSFS implements a mechanism to mount any file system format on a distributed grid with quantum-secure properties.

The storage layer architecture consists of three primary components:

- [0-db](https://github.com/threefoldtech/0-db): The core storage engine implementing an append-only database that stores objects in immutable format. This design enables built-in history preservation, optimized disk performance, minimal overhead, simplified data structures, and efficient backup operations through linear copying of immutable files.

- [0-stor-v2](https://github.com/threefoldtech/0-stor_v2): Handles data dispersion by implementing 'forward-looking error-correcting code' (FLECC) algorithms. The component processes files in any format, applies AES encryption using user-defined keys, performs FLECC encoding, and distributes the resulting fragments across multiple 0-DB instances. The fragment generation is configurable to adjust redundancy levels based on availability requirements. The system maintains data accessibility even when some 0-DB instances are unavailable, and can rebuild missing fragments to restore full consistency.

- [0-db-fs](https://github.com/threefoldtech/0-db-fs): Implements the filesystem driver interface using 0-DB as its primary storage engine. It manages directory and metadata storage in a dedicated namespace, with file payloads stored in a separate namespace.

These components create a quantum-secure storage layer where no single node contains sufficient information to reconstruct the complete data, providing protection against even advanced computational attacks.

The architecture supports virtually unlimited scaling and compatibility with various storage interfaces:
- S3-compatible storage
- Backup systems
- FTP servers
- IPFS and Hypercore distributed file sharing protocols

### Deployment Architecture

The filesystem can be deployed within virtual machines or Kubernetes environments, enabling users to implement various storage applications such as Minio for S3-compatible storage or OwnCloud for web-based file serving.

The architecture supports diverse storage workloads on the distributed storage system.

### Current Limitations

The current implementation requires technical expertise for deployment and management. This accessibility limitation will be addressed in the upcoming V4 release.
