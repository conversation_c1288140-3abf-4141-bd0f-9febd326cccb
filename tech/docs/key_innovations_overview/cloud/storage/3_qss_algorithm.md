---
sidebar_position: 3
---

# Zero Knowledge Quantum Safe Encoding

The Quantum Safe Storage (QSS) Algorithm forms the core of the distributed storage engine. This algorithm processes original data objects and generates mathematical representations that are distributed across multiple virtual storage devices (ZDBs).

The algorithm implements a distribution pattern that ensures data integrity even in the event of partial system failure.

Key technical features:

- Append-only data structure preventing data loss
- Post-quantum cryptographic protection
- Geographic data distribution with high availability during site failures
- Protection against data degradation (bitrot)

## Technical Challenge

Current data production volumes are increasing exponentially. Traditional replication-based approaches to reliable storage do not scale efficiently. A paradigm shift is required from securing entire datasets to securing the individual objects that comprise a dataset.

The algorithm leverages mathematical principles originally developed for space communications.

The implementation distributes data fragments across multiple physical storage devices using a mathematical transformation approach rather than simple replication or sharding.

## Conventional Storage Architecture

![](img/storage_old.png)

Most distributed storage systems currently deployed on the Internet or in blockchain implementations rely on data replication (sometimes following sharding, where data is distributed based on content characteristics across geographic locations).

This architecture results in significant storage overhead and limited control over data placement.

Even in optimized implementations, storage overhead typically reaches 400%, while less efficient systems may require orders of magnitude more storage to achieve comparable redundancy levels.

## QSS Mathematical Approach

![](img/unbreakable2.png)

The QSS algorithm implements a more efficient approach with enhanced reliability and precise control over data placement.

This implementation can be visualized using a mathematical equation model:

Let a,b,c,d... represent fragments of the original data object. The system generates multiple unique equations using these fragments. For example, with three original fragments having values:

```
a=1
b=2
c=3
```

(In actual implementation, each fragment is represented by a complex binary sequence such as `110101011101011101010111101110111100001010101111011.....`)

The algorithm generates multiple equations from these values:

```
1: a+b+c=6
2: c-b-a=0
3: b-c+a=0
4: 2b+a-c=2
5: 5c-b-a=12
...
```

Mathematically, only three equations are required to fully describe the original fragments, but generating additional equations enhances reliability. These equations are distributed across physical storage devices, with each device storing a single equation. The original data is not retained; only the locations of the distributed equations are tracked.

To recover the original data, any three equations from the total set are mathematically sufficient. When data is requested, the system retrieves equations from multiple locations, and the first three responses enable reconstruction of the original values. For example, with these three randomly retrieved equations:

```
5c-b-a=12
b-c+a=0
2b+a-c=2
```

The system solves this equation set:

- First: `b-c+a=0 -> b=c-a`
- Second: `2b+a-c=2 -> c=2b+a-2 -> c=2(c-a)+a-2 -> c=2c-2a+a-2 -> c=a+2`
- Third: `5c-b-a=12 -> 5(a+2)-(c-a)-a=12 -> 5a+10-(a+2)+a-a=12 -> 5a-a-2=2 -> 4a=4 -> a=1`

With `a=1` determined, the system calculates `c=a+2=3` and `b=c-a=2`, successfully reconstructing the original data fragments.

This approach achieves redundancy with significantly lower overhead by storing equations rather than data copies. The actual implementation operates on thousands of fragments rather than just three.

### Implementation Example: 16/4 Configuration

In a typical configuration, each object is fragmented into 16 parts. These 16 original fragments require 16 equations to mathematically describe them. The system generates 20 equations and distributes them across 20 devices. Data reconstruction requires only 16 equations, meaning any 16 of the 20 stored equations are sufficient to recover the original data. This architecture can tolerate the loss of any 4 of the 20 equations.

The probability of simultaneous failure of 4 independent, geographically distributed storage devices is statistically minimal. The system continuously monitors all stored equations and can generate additional equations immediately upon detecting a missing equation, implementing automatic data regeneration and self-repair capabilities.

> This implementation achieves an overhead of only 4 out of 20 (20%) compared to the 400% overhead typical in traditional replication systems.

## Content Delivery Implementation

This algorithm provides an efficient backend for content delivery networks.

For example, a content distribution policy might specify a 10/50 configuration, meaning a video file would be distributed across 60 locations with the ability to lose 50 locations simultaneously without data loss.

When a user requests the content, responses from the first 10 locations provide sufficient data fragments to reconstruct the complete file.

While this configuration has higher overhead than the previous example, it remains significantly more efficient than conventional CDN architectures.

## Data Degradation Protection

The algorithm addresses data degradation (bitrot), which causes storage media to become gradually unreadable over time.

The implementation detects and corrects silent data corruption, ensuring long-term data integrity.

> For additional information on data degradation: https://en.wikipedia.org/wiki/Data_degradation
