---
title: Zero-Install Architecture
sidebar_position: 4
---

![](../../../img/boot.png)  

The Zero-OS implements a network boot architecture that delivers the operating system to nodes over the internet, eliminating local installation requirements.

### Node Deployment Process

1. Deploy compatible hardware.
2. Configure a resource farm on the grid explorer interface.
3. Implement the bootloader via USB media or configure network boot infrastructure.
4. Establish internet connectivity and power on the hardware.
5. Initiate boot sequence. The system automatically retrieves operating system components (Zero-OS) from the network.

The bootloader implements a minimal footprint design that initializes network interfaces and queries the grid for required boot components.

The operating system operates without installation on local storage media (hard disk, SSD), implementing a stateless architecture.

This secure and efficient boot process is enabled by the container virtual filesystem architecture.

### Technical Implementation

- **Optional Security Enhancement**: Configure secure boot parameters in system BIOS.
- **Optional Cryptographic Verification**: Install signing certificates in BIOS to restrict boot process to cryptographically verified bootloaders.
- **Bootloader Retrieval**: The bootloader (supporting ISO, PXE, USB protocols) is retrieved from the content delivery network or private deployment infrastructure.
- **Core Initialization**: The primary boot process (Core-0) initiates and performs self-verification procedures.
- **Metadata Verification**: Required software module metadata is retrieved and verified against cryptographic signatures and hash values.
- **Service Initialization**: The Core-0 zero image service initializes to manage container deployments.
