---
title: Zero-Images Architecture
sidebar_position: 3
---

![](../../../img/zos_images.jpg)


### Technical Challenge

Current cloud workload deployment methodologies using Docker containers and virtual machine images present several technical limitations. These images consume substantial storage resources, create inefficient and bandwidth-intensive transfers to edge computing environments, increase operational costs, introduce unnecessary complexity, and present security vulnerabilities due to content verification challenges over time.

For quantitative comparison, a standard Ubuntu image typically requires approximately 2 GB of storage and contains millions of files. In contrast, the Flist (metadata structure for Zero-Image) for an equivalent Ubuntu image requires less than 2 MB (a 1000x reduction). The architecture downloads only required files based on this metadata, typically reducing transfer volume by 10x compared to traditional image sizes. Downloaded components are verified through cryptographic fingerprinting (hash validation) before execution.


### Technical Implementation

- The Zero-OS or Zero-Image Command Line interface (Linux-compatible) receives instructions to provision a virtual filesystem based on a Zero-Image URL reference.
- Zero-Image metadata is stored on standard S3-compatible storage or the specialized Zero-Hub infrastructure.


### Flist Technical Architecture

This implementation introduces a new image format that decouples image data (files and subfile components) from the metadata describing the image structure.

The Flist format incorporates comprehensive file descriptions with complete metadata including size parameters, modification and creation timestamps, and POSIX attributes. Each component includes a cryptographic fingerprint, ensuring deterministic execution behavior—a critical requirement for security-focused implementations.

The architecture enables independent management of metadata and data components, providing flexibility for various build and deployment scenarios.

### Technical Advantages

- **Deployment Efficiency:** The architecture enables container and virtual machine initialization up to 100x faster than traditional approaches, particularly in distributed computing environments.
- **Security Enhancement:** The implementation prevents image tampering through cryptographic verification, significantly improving security posture.
- **Resource Optimization:** The architecture reduces storage and bandwidth requirements by up to two orders of magnitude compared to conventional approaches.
- **Deterministic Execution:** Engineers can define precise deployment parameters in advance, ensuring predictable execution without runtime modifications.
- **Standards Compatibility:** The implementation maintains compatibility with existing standards, Docker containers, and virtual machines. The format is applicable to both VM and container technologies.

### Implementation Status

The technology has been in production use for multiple years. Additional technical details are available in the [Zero-OS documentation](zos_compute.md).
