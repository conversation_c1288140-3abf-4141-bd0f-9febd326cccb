---
title: Enterprise Roadmap
sidebar_position: 20
---


# Government, Commercial Hosters, Telco and Enterprise Roadmap

We are working on the government, commercial hosters, telco and enterprise releases of our technology.

> 90% of the work has been done as part of our base offering but we need additional features for enterprises.

## Enterprise User Interface

The current user interface is designed for an open-source tech audience. For enterprise use, we need a different approach to meet the unique needs of enterprise environments:

- **Private or Hybrid Context**: All operations should be conducted within a private or hybrid cloud context to ensure security and compliance.
- **Enhanced Monitoring**: We need more comprehensive monitoring dashboard screens to provide real-time insights and analytics.
- **Identity Management Integration**: Integration with enterprise-grade Identity Management solutions, such as LDAP, Active Directory, and SSO (Single Sign-On), is essential.
- **Enterprise-Friendly UI**: The user interface needs to be redesigned to be more intuitive and tailored to enterprise users, focusing on usability and efficiency.
- **Token Irrelevance**: Tokens are not a priority in this context and should be de-emphasized in the solution.

## Windows Support

The virtual Machine technology we use does support Windows, but we need to do some further integration.

## High Performance Network Integration

- **Local Network Integration**: Zero-OS is designed to support a wide range of technologies, though additional integration work is required to optimize performance.
- **High-Speed Backbones**: We aim to support high-speed Ethernet and RDMA (Infiniband) based backbones.
- **Instrumentation Enhancements**: Additional instrumentation needs to be incorporated into Zero-OS to achieve optimal performance.
- **Target Performance**: Our goal is to achieve network speeds exceeding 100 Gbps.
- **Custom Integration**: We offer integration with selected network equipment from our customers, accommodating custom integration requirements.

## High Performance Storage Block Device Integration

Next to the existing already integrated storage backends we want to support a high performance redundant storage block device.

- High performance redundant storage network
- Supports our high speed backbone as defined above
- Scalable to thousands of machines per cluster.
- Replication capability between zones.
- Custom Integration
  - We offer integration with selected storage equipment from our customers, accommodating custom integration requirements.

## Service Level Management

- The system will have hooks and visualization for achievement of Service levels.
- This will allow a commercial service provider to get to higher revenue and better uptime management.

## Support for Liquid Cooling Tanks

- Do a test setup in liquid cooling rack or node.
  - We can use our self-healing capabilities to manage in a better way.
- This is an integration effort, and not much code changes are needed.