---
title: "TFGrid Roadmap"
sidebar_position: 3
---


## TFGrid High Level Roadmap

### Status Today

The core offering is functioning effectively, maintained through a community-driven, best-effort approach. Currently, 
there are no Service Level Agreements (SLAs) in place, and there should be increased visibility for users regarding their expectations for uptime, performance, and other service related requirements.

The uptime and stability of Zero-OS are very good. 

Additionally, hardware compatibility is excellent, with most machines now supported out of the box.


|                         | Status today                                                                                                 | SDK/API | Web UI |
| ----------------------- | ------------------------------------------------------------------------------------------------------------ | ------- | ------ |
| Zero-OS                 | Used for management of +30,000 logical CPU cores                                                             | yes     | yes    |
| Zero-Images (flists)    | Basis for Zero-OS modules as well as replaces images for VM's ...                                            | yes     | yes    |
| Zero-Images from Docker | convert docker through our Hub                                                                               | yes     | yes    |
| Zero-Images Hub         | ThreeFold is hosting some as well as everyone can install their own Hub                                      | yes     | yes    |
| Mycelium Core           | Integrated in Zero-OS for VM's as well s ZDB and monitoring                                                  | yes     | yes    |
| Mycelium Message Bus    | Can be used by any developer for their own usecases                                                          | NA      | NA     |
| Unbreakable Storage     | Usable for experts only, is reliably working for +6 years, +100 MB/sec per stream                            | yes     | no     |
| Unbreakable Filesystem  | QSFS= usable for experts, is a fuse based filesystem on top of the QSS Core                                  | yes     | no     |
| Zero-OS Kubernetes      | Working very well, Integrated in ZOS, uses our overlay networks based on Wireguard, can use QSFS underneith. | yes     | yes    |
| Zero-OS VM's            | The base of our service portfolio, missing is better service level management                                | yes     | yes    |
| Zero-OS Monitoring      | Working well                                                                                                 | yes     | yes    |
| Zero-OS VM Monitoring   | Working well, can be retrieved through SDK                                                                   | yes     | yes    |
| Zero-OS Web Gateway     | Working well, but documentation not good enough, and not enough of them deployed                             | yes     | yes    |
| Zero-Boot               | There are multiple ways active on how to deploy Zero-OS all are stateless and capable for full secure boot   | yes     | yes    |

### Planned new features:

Considerable effort is being made to enable our partners to go into production; 
however, for this initiative to truly succeed on planetary level, we need many more nodes deployed in the field.

Below you can find some of the planned features of TFGrid 4.0 mainly to achieve ability to scale to hundred of thousand of nodes.

|                                     | Roadmap                                                         | Timing  |
| ----------------------------------- | --------------------------------------------------------------- | ------- |
| Zero-OS v4 (our next major release) | V4, without TFChain, mutual credit, marketplace                 | Q2/3 25 |
| Zero-Images from Docker             | CI/CD integration (See 3AI CI/CD)                               | Q1 25   |
| Zero-Images Hub Integration         | CI/CD integration (See 3AI CI/CD) no more need for separate Hub | Q1 25   |
| Mycelium Core                       | Just more hardening and testing                                 | Q1 25   |
| Mycelium Message Bus                | Replace our current RMB, all our own RPC over Mycelium          | Q1 25   |
| Zero-OS VM's Cloud Slices           | Integration 3AI CI, use cloud slices to manage                  | Q2 25   |
| Zero-OS Monitoring Docu             | More docu and easier API                                        | Q2 25   |
| Zero-OS Web Gateway Expansion       | Need more deployed, better integration with new Mycelium        | Q2 25   |
| Mycelium Names                      | In V4, name services                                            | Q2 25   |
| Zero-OS Cloud,Storage,AI Slices     | As part of marketplace for V4, flexible billing mutual credit   | Q3 25   |
| FungiStor                           | A revolutionary different way how to deliver content            | Q3 25   |
| Zero-Images on FungiStor            | Can be stored on FungiStor                                      | Q3 25   |
