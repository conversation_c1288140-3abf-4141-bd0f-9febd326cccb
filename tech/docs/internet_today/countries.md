---
title: 'National Security'
sidebar_position: 3
hide_title: true
---

![](img/atomic_race.png)

## Countries Digital National Security is Compromised

> In our Opinion, there are 3 matters of national security to be considered.

### Geo Politics in Digital

Over 90% of the world depends on the digital infrastructure/software/platforms controlled by a single superpower, including cloud services, internet cables, and platforms like Google and Microsoft. 

This creates extreme vulnerability: if leveraged, the superpower could disrupt economies, cut off access, or manipulate systems to force compliance. Such dependence grants it immense geopolitical power, turning digital infrastructure into a tool for coercion and threatening global sovereignty.

**Questions to ask yourself**

- **How much of your critical infrastructure, data & apps are hosted in the cloud?**  
  - The **U.S. owns 99%** of global cloud capacity.  
  - Even if a U.S. company builds a data center in your country:  
    - They **still have full control**.  
    - They **will use** that power if they believe they need to.  
    - **See**: [CLOUD Act](https://www.justice.gov/criminal/cloud-act-resources).  
- **Who comes first?**  
  - Do you believe the **U.S. will always put its own interests first?**  
  - Could there come a time when **your country or currency is less aligned**?  
  - If yes, what is the chance that the U.S. would:  
    - Use its cloud services as **leverage**?  
    - Restrict access to critical tools?  
    - Enforce economic or political pressure?  
- **Real-world examples:**  
  - **[DeepSeek AI](https://www.computerweekly.com/news/*********/US-lawmakers-move-to-ban-DeepSeek-AI-tool)** – U.S. lawmakers moving to ban foreign AI tools.  
  - **China tariffs** – Trade restrictions based on geopolitical interests.  
  - **Mexico** – U.S. economic and trade pressures.  
  - **Other cases** – Numerous examples of digital and economic influence.  
- **What’s the risk?**  
  - If your businesses **lose access** to these cloud services:  
    - How many of your **systems would go down**?  
    - How dependent are you on **foreign-controlled infrastructure**?  
  - Even if unlikely, **can you afford the risk?**


### Disasters (war, climate)

The global internet system is dangerously fragile due to its centralized structure and reliance on outdated protocols like TCP/IP, which were designed decades ago. There are many points of centralization (internet exchange points, name services, datacenters, cloud services, ...) leading to very vulnerable situation. 


A single outage or attack on these systems **will** cripple a nation’s infrastructure, from banking, airports to healthcare, leaving it defenseless. The system’s vulnerability to both human-made and natural threats highlights the urgent need for decentralized, secure, and resilient alternatives.

**Questions to ask yourself**

- **Do you expect climate change to impact your critical infrastructure?**  
  - If yes, could this disrupt **connections to the U.S. and Europe**, where your apps are hosted?  
  - Could extreme weather events increase the risk of **cable cuts**?  
  - **See appendix** for proof points and their impact.  
  - Do you want to take the risk?

- **Is the current internet infrastructure outdated?**  
  - The internet was not designed for today’s demands—it functions **like a traffic jam** under heavy load.  
  - Do you believe outdated technology could cause **even more issues** in the future?  

- **Who controls the infrastructure?**  
  - Do you believe that **U.S. companies play the biggest role** in running global internet infrastructure?  
  - If two people in your country join a **Zoom call**, their connection **routes through the U.S. or Europe**—is this how it should work?  

- **Are you losing money by relying on foreign internet capacity?**  
  - Do you agree that **not having your own infrastructure** means:  
    - **You loose billions of revenue which should stay in country.**
    - You pay more for bandwidth.  
    - Your economy depends on **foreign-controlled** systems.  
    
> **[More info](https://threefold.info/tech/docs/category/internet-is-broken).**  



### Cyberpandemic

Intelligence agencies worldwide have embedded backdoors into various systems—an approach that may have once seemed beneficial but has since led to numerous unintended consequences.

With the advent of AI and quantum computing, securing centralized systems using traditional methods has or will become virtually impossible, unless we completely rethink the way how to create our digital future.

The **Cyberpandemic** is now a reality.

**Questions to ask yourself**

- **Do you believe intelligence agencies have embedded backdoors into various systems?**  
  - If yes, do you think this has led to **unintended consequences**?  
  - Do you trust that these backdoors will **never be exploited** by hackers or worse?  

- **Can traditional security methods protect centralized systems in the AI & quantum era?**  
  - Do you think AI and quantum computing will make **current security models obsolete**?  
  - If so, do we need to **completely rethink** how we build digital infrastructure?  
  - Do you believe that using bolted on solutions will help often provided by Israel or US?

- **Is a **Cyberpandemic** already happening?**  
  - Are you seeing **increasing cyberattacks** on critical infrastructure?  
  - Do you believe **centralized systems** are a major security risk?  
  - Do you have a **contingency plan** if key systems are compromised?  

> **[More info](https://threefold.info/cyberpandemic).**  

## THIS IS A MATTER OF NATIONAL SECURITY

> **It's not a question of **"if"**, but "**when**".**

- **Is building local datacenters a real solution?**  
  - It is **expensive and complicated**
  - Even if built, would it truly solve the problem?  
  - The software, apps, and systems **are still controlled by U.S./China-linked companies**.  
  - Most existing tech is based on **outdated paradigms**.  

- **Can hosting U.S. cloud infrastructure locally ensure control?**  
  - Even if U.S. companies **host data in your country**, do they still hold the keys?  
  - Does any contract **truly prevent them** from using their control when they deem necessary?  

- **What about European companies?**  
  - Even if EU-based, do they still **rely on U.S. cloud infrastructure**?  
  - Is the tech stack **still dependent on U.S./Chinese technology**?  
  - Does this mean the problem **remains unsolved**?
  
- **Is quickly rolling your own infrastructure a viable solution?**  
  - It would **take years** and come with **high risks**.  
  - Would it still be **based on outdated paradigms**?  
  - Does any solution **using the current way of thinking** truly resolve the issue at its core?  

## Lets' act.

A group of individuals, has developed a system designed to overcome the challenges as described above.  

Built from the ground up on a fundamentally different paradigm, this system offers:  

- **Geographic awareness** for optimized performance and resilience  
- **100x greater reliability** compared to traditional architectures  
- **Robust protection against AI- and quantum-based threats**  
- **Drastically lower energy consumption** for sustainable operation  
- **Extreme scalability**, enabling significantly more efficiency with fewer resources  
- **10x cost-effectiveness**, making advanced technology accessible to a broader audience  
- **Augmented Collective Intelligence**, optimized safe Intelligence for our future.


## A Digital "Atomic Race" is going on.

We are witnessing a digital world war, a global race for dominance in "super" intelligence (AI) that mirrors the 20th-century competition for atomic power. 

Nations and corporations are investing heavily in AI to secure economic, military, and geopolitical advantages, as AI promises to redefine industries, enhance cyber and physical warfare, and shape global power dynamics. 

Just as atomic power redefined the balance of power in the Cold War, super intelligence is becoming the cornerstone of 21st-century strategy, with the stakes including national security, economic prosperity, and technological sovereignty. The race to develop and control AI technologies is not just about innovation but about ensuring that these advancements are harnessed responsibly to avoid misuse, ethical dilemmas, and escalating global tensions. 

### Super intelligence is much more than AI

Its the combination of:

- AI LLM's (Large Language Models)
- Self Healing Autonomous Agents (AI is only subset)
- Quantum Compute, Encryption, ... 
- Access to Self Healing Geo-Aware and Sovereign Internet Systems (network, storage, compute)
- Synthetic biology (this is like playing god)

### It's on

Superpower(s) are pouring massive funds to dominate in these fields, focusing on its own economic and military gains while ignoring broader global impacts. 

Other nations are left behind, widening global inequality and creating a divide between technological leaders and those struggling to compete. This strategy prioritizes self-interest over international cooperation.

Over 90% of nations have no chance to compete in this race unless they redefine the rules of the game.

## Augmented Collective Intelligence

Artificial Intelligence (AI) lacks ethical understanding, cultural sensitivity, and human intuition—critical for global leadership. ACI enhances decision-making by integrating human wisdom with machine intelligence, avoiding AI’s limitations.

AI is centralized, prone to bias, and easily weaponized, while ACI fosters decentralized, transparent, and resilient governance. It enables leaders to adapt to uncertainty, unlike AI, which relies solely on past data.

AI’s high energy demands and monopolized control make it unsustainable. ACI optimizes intelligence, reducing resource consumption while ensuring stability and a safer "digital" future. 

## The Future: Leadership Augmented, Not Replaced

Global leaders cannot afford to be passive recipients of AI-driven conclusions.

Instead, they must leverage Augmented Collective Intelligence to:

- Enhance human judgment rather than automate it away
- Empower decentralized problem-solving rather than rely on centralized AI control
- Ensure transparent, ethical, and inclusive decision-making

# The way forward.


We are carefully considering how to release our breakthrough technology and the powerful ideas behind to countries who need it the most.

Initially, we considered a fully decentralized approach but realized it wouldn’t drive change quickly enough.

Our current plan is to establish 2 a 3 **well energised technology centers** to further develop the technology and ensure its accessibility within their regions.  


## Apendix


### The Internet gets disrupted

Here are four notable incidents where undersea internet cables were cut, along with their impacts just over the last year:

#### **2024 Red Sea Cable Cuts**

- **Date**: February 2024
- **Location**: Red Sea
- **Details**: Multiple submarine cables, including AAE-1, SEACOM, and EIG, were damaged, possibly due to a ship's anchor dragging along the seabed.
- **Impact**: Approximately 25% of internet traffic between Europe, Asia, and the Middle East was affected, leading to downtime and unusable internet for a lot of people.

#### **2024 Baltic Sea Cable Disruptions**

- **Date**: November 2024
- **Location**: Baltic Sea
- **Details**: The BCS East-West Interlink and C-Lion1 submarine cables were severed, with suspicions pointing towards a Chinese-registered vessel, the Yi Peng 3, possibly dragging its anchor.
- **Impact**: Despite the damage, significant redundancy and resilience in Europe's internet infrastructure mitigated major disruptions.

#### **2025 Taiwan Undersea Cable Incident**

- **Date**: January 2025
- **Location**: Northeast of Taiwan
- **Details**: The Chinese-owned vessel Shunxing 39 was suspected of damaging an undersea internet cable by dragging its anchor.
- **Impact**: While services in Taiwan remained mostly uninterrupted due to quick rerouting by Chunghwa Telecom, the incident highlighted vulnerabilities in undersea infrastructure amid geopolitical tensions.

#### **March 14, 2024: West Africa Cable System (WACS) and Africa Coast to Europe (ACE) Cable Cuts**

- In 2024, South Africa experienced significant internet disruptions due to undersea cable damages:
- **Cause**: A suspected underwater rockslide off the coast of Côte d'Ivoire damaged multiple submarine cables, including WACS and ACE, or could this have another reason?
- **Impact**: Thirteen African countries, including South Africa, faced degraded services or near-total internet outages. Lots of countries had no or limited internet for up to a week.

These incidents underscore the vulnerability of undersea cables and the need for geo-aware Internet / AI Capacity in country