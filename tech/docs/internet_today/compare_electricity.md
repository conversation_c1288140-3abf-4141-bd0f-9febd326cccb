---
sidebar_position: 1
---
# Something to Think About...

## Would a Country Do This?

![](img/electricity.png)

Imagine this: Would it make sense to rely on electricity that’s generated far away, on the other side of the world? You’d need a super expensive cable to bring it to you, and if that cable breaks, you’d lose power completely. No country would ever choose to do this because it’s costly, inefficient, and risky.


## Why is +70% of the World Doing it for the Internet?

![](img/we_are_doing_it_for_internet.png)



Now think about the internet. That’s exactly what most of the world is doing! Over 70% of the world depends on internet infrastructure that’s far away, requiring expensive cables and systems to bring it to users. Here’s why this doesn’t make sense:

1. **It’s Too Expensive**  
   Using distant infrastructure means you’re paying not only for the internet service itself but also for the costly cables and systems to deliver it. This makes it much more expensive than building local infrastructure.

2. **It’s Vulnerable**  
   A single cable or system can fail because of natural disasters, accidents, or even sabotage. If that happens, millions of people could lose access to the internet.

3. **It Compromises Control**  
   Relying on systems controlled by other countries or big companies means you have less independence. They control your access to the internet and your data.

4. **It’s Inefficient**  
   Just like it’s smarter to generate electricity close to where it’s used, it’s also better to host internet services closer to the people using them. This makes things faster, cheaper, and more reliable.

---

Instead of relying on far away systems, we should build local, decentralized internet infrastructure. It’s safer, more affordable, and gives people more control over their digital lives.