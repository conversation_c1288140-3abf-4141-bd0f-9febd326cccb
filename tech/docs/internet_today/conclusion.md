---
title: 'Conclusion'
sidebar_position: 10
---

![](img/conclusion.png)

Only 50% of world has decent access to Internet, let's recap the issues.

### **1. Centralization Risks**

- **Dependence on Few Entities:** Countries and individuals heavily rely on centralized providers like Google, Amazon, and Microsoft for critical services, creating vulnerabilities to disruptions, geopolitical conflicts, and external control over data and infrastructure.
- **Loss of Autonomy:** Centralized data centers and infrastructure compromise autonomy, leaving nations and organizations at the mercy of foreign entities and global policies.
- **Fragility:** The current centralized model leads to single points of failure, where disruptions can have widespread economic and societal impacts.

---

### **2. Internet Inefficiency**
- **Long-Distance Data Transfer:** Much of the world depends on internet infrastructure located far away, requiring data to travel unnecessarily long distances, increasing costs and reducing reliability.
- **Underutilized Hardware:** Modern computing systems fail to efficiently utilize hardware advancements due to inefficiencies like excessive context switching, leading to wasted resources and performance bottlenecks.

---

### **3. Economic and Structural Challenges**
- **GDP Negative Impact:** Developing nations face economic disadvantages due to the internet's structure. Revenue is lost to global platforms (e.g., booking sites, advertising), creating economic leakage and dependency.
- **Infrastructure Costs:** Developing countries disproportionately bear the cost of accessing global internet infrastructure without reaping proportional benefits.

---

### **4. Technological and Architectural Flaws**

![](img/problem_overview.png)
- **Outdated Protocols:** TCP/IP, the foundational internet protocol, was not designed for modern needs like dynamic networks, security, and session management, leading to inefficiencies and vulnerabilities.
- **Layer Complexity:** The current "onion-like" stack of layers in cloud and internet architecture adds unnecessary complexity and fragility, masking core problems rather than addressing them.


---

### **5. Less than 50% of world has decent internet**

![alt text](img/fortune.png)

Finally, we should not forget that  the internet is only available to 50% of the world!