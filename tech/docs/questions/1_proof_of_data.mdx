---
title: Proof of Data
sidebar_position: 20
---

import { Kroki } from '@site/src/components/Kroki';

**Question:**

> *How do you enforce honesty, i.e. how do you prevent me from running a node that claims it has 5PB local storage but doesn't actually store anything, and upon receiving a request for data it just returns data out of /dev/random?*

**Answer:**

Enforcing honesty in a decentralized system requires a layered approach, and we’ve built multiple mechanisms into our decentralized cloud architecture to ensure data integrity, authenticity, and availability.

In short, our decentralized cloud enforces honesty through a mix of:

- Cryptographic verification (TPM2, signatures)
- Hardware validation
- Smart contract auditing
- Real proof-of-storage and usage
- Data redundancy and self-healing

Faking it isn’t just hard — it’s economically unviable.

### **Incentive Design & Economic Deterrence**

- There’s **no real economic benefit** to pretending. In v3, even if someone reverse-engineered ZOS and faked components, they’d still **only be rewarded if their node is usable**.
- Fraudulent nodes lose reputation quickly and become unusable by the network — making the attack short-lived and costly.
- In v4 incentivation is mainly based on utilization of the system (80% of revenue goes to farmer), if system doesn't work properly no incentive. In v4 there are also rewards for proof of capacity but there is an advanced auditing system for detecting bad actors.

---

### **Architecture**

<Kroki>
{`
actor User
package "Secure Sandbox (User-side)" {
  component QSFS as "QSFS"
  component ZeroStor as "Zero-STOR"
}

package "Farmer Nodes" {
  node Node1 as "Node A (Zero-OS)" {
    component ZDB1 as "ZDB A"
  }
  node Node2 as "Node B (Zero-OS)" {
    component ZDB2 as "ZDB B"
  }
  node Node3 as "Node C (Zero-OS)" {
    component ZDB3 as "ZDB C"
  }
}

entity Blockchain
entity Validator

User --> QSFS : Request to store/retrieve file
QSFS --> ZeroStor : Read/write fragments

ZeroStor --> ZDB1 : Store/verify fragment
ZeroStor --> ZDB2 : Store/verify fragment
ZeroStor --> ZDB3 : Store/verify fragment

ZeroStor --> Blockchain : Report storage proofs
Validator --> Blockchain : Trigger audits
Validator --> ZDB1 : Run audit workloads
Validator --> Blockchain : Update reputation

@enduml

`}
</Kroki>

- **Zero-STOR is not part of the infrastructure where the ZDBs are located. It runs in the user's secure environment and is responsible for verifying and checking the integrity of the data. Any fraudulent behavior is immediately detectable.**
- **It's important to note that a ZDB has no knowledge of the original data source, and it is physically impossible to reconstruct the original data from individual fragments alone.**

---

### **Data Cannot Be Faked**

- The encoding algorithm we use guarantees data integrity.
- **Even 1 bit difference** between original and stored data will be flagged as corrupted — data from such nodes won’t be used in recovery.
- This allows us to **invalidate** dishonest storage (ZDBs) and **detect fraud**, even if not actively done today.

--- 

### **Security and Attestation at the Core**

- **Zero-OS (ZOS)** enforces a secure boot chain and checks the full software integrity. Only verified modules (via flists) can be deployed. This forms a trusted base.
- **TPM2 (Trusted Platform Module)** on the motherboard is used to sign and verify the authenticity of the node's hardware and software stack.
- **Regular Capability Checks** are performed (every X minutes) to verify actual system capabilities: CPU, memory, disks, network, IPMI, etc. These are cryptographically signed and recorded on-chain.

---

###  **Storage: Proofs, Fingerprints, and Audits**

- **ZDB (Zero-DB)**, our low-level storage module, is fingerprinted before launch. Only the correct, untampered version is allowed to run.
- ZDB doesn’t just claim storage — it **writes real data and verifies it**. Any attempt to spoof storage (e.g., with a fake ZDB) would fail validation.
- **Zero-Stor** can ask for the hash of any stored fragment. Since hash verification requires actual data, it's a robust proof-of-storage mechanism.
- Data is stored in small fragments across the grid, and even a single bit flip (e.g., bitrot or tampering) is detected by Zero-Stor. Corrupt fragments are ignored during reconstruction.

---

###  **Auditing and Smart Contracts**
- **Randomized auditing runs** are initiated by validators at unpredictable intervals.
- These audits test real usage: deploying VMs, writing/reading ZDB storage, network performance, etc.
- Results feed into a **reputation system** tied to each node, governed by our IT smart contracts — tampering with this would require breaking the contract logic.

---

