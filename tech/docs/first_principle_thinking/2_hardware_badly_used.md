---
title: 'Hardware Is Powerful'
sidebar_position: 6
hide_title: true
---

# Hardware Is Powerful

![](img/hardware_comparison.png)


### The IT world fails to harness the full potential of computer hardware.


While hardware advancements have surged forward, user experiences and features have often stagnated, failing to keep pace with these developments.

The original Commodore 64, with only 64 KB of memory, was a remarkably capable machine for its time. In contrast, today's computers have 8 GB or more of memory, yet their capabilities have not necessarily improved proportionately.

This highlights a regression in our ability to fully utilize computer hardware.

We are committed to bridging this gap by optimizing our approach to hardware utilization, thereby unlocking its full potential. 

## Why Are Servers So Badly Used?

![](img/layers.png)

Context switches occur when a computer's processor shifts from executing one task (or context) to another. While necessary for multitasking, too many context switches lead to inefficiency, as demonstrated in this diagram. Below, we provide a simplified explanation.

## Why Context Switches Are a Problem

### What Are Context Switches?

Imagine you're working on two tasks: reading a book and answering emails. Every time you switch between them, you lose time refocusing. Computers experience a similar "refocusing" delay when switching between tasks.

### The Layered Architecture Causes Overhead

Modern computing systems use many layers (e.g., applications, storage drivers, network layers) to get work done. Each layer requires the system to switch between different modes (user mode and kernel mode) and tasks.

For example:

  - A web app might need to talk to a storage driver.
  - This requires moving data through multiple layers (network, file system, etc.).
  - Each layer adds a context switch.

### Millions of Switches Per Second

Each switch requires saving and loading the state of a process. This takes time and uses CPU power. When millions of context switches occur every second (as shown in the diagram), most of the computer’s capacity is spent switching rather than doing useful work.

### Result: Wasted Resources
   
- Sometimes up to 90% of the computer’s capacity can be lost because of this inefficiency.
- Instead of performing tasks like running applications or processing data, the computer is stuck managing unnecessary complexity.


## Simplified Analogy

Imagine driving on a highway where you have to stop and pay a toll at every intersection. You waste more time paying tolls than actually driving to your destination. Similarly, excessive context switches in modern systems cause the computer to "stop and pay tolls" constantly, leaving little time for real work.


## How Did We Get Here

![](img/eng_model_failing.png)


## How to Resolve This Problem

We decided to reinvent the way an operating system functions in a cloud and data center context.
While leveraging the Linux kernel, we rebuilt many surrounding tools to minimize complexity and reduce context switching.
This approach results in significantly improved security, scalability, and sustainability.

> [See here for more information about our operating system called Zero-OS](../key_innovations_overview/cloud/compute/zos_compute.md)

### Context Switching Details 

In the context of CPU scheduling in Linux (and in most modern operating systems), a context switch refers to the process of saving the state of a currently running process (such as its registers, program counter, and other relevant information) and loading the state of a different process to allow it to run. This switching of execution from one process to another is a fundamental aspect of multitasking operating systems, where multiple processes share the CPU's time.

### How It Works

Here's how a context switch typically works in Linux:

1. **Interrupt Handling**: When a higher-priority process needs to run or an event requiring immediate attention occurs (such as I/O completion), the CPU interrupts the currently running process.

2. **Saving Context**: The operating system saves the state of the current process, including its registers, program counter, and other relevant data, into its process control block (PCB). This step ensures that when the process resumes execution later, it can continue from where it left off.

3. **Scheduling Decision**: The operating system scheduler determines which process should run next based on scheduling algorithms and the priority of processes in the system.

4. **Loading Context**: The operating system loads the state of the selected process from its PCB into the CPU, allowing it to execute. This includes restoring the process's registers, program counter, and other relevant data.

5. **Execution**: The newly loaded process begins executing on the CPU.

### Overhead and Performance Impact

Context switches are essential for multitasking, but they come with overhead that can impact system performance:

1. **Time Overhead**: Context switches require time to save and restore process states, as well as to perform scheduling decisions. This overhead adds up, especially in systems with many processes frequently switching contexts.

2. **Cache Invalidation**: Each time a process is switched in, it may result in cache invalidation, where the CPU's cache needs to be refreshed with data from the new process's memory space. This can lead to cache misses and performance degradation.

3. **Resource Contentions**: Context switches can exacerbate resource contention issues, especially in systems with limited CPU cores. If multiple processes are frequently contending for CPU time, the overhead of context switches can further delay process execution.

4. **Fragmentation**: Frequent context switches can lead to memory fragmentation, as processes are loaded and unloaded into memory. This fragmentation can degrade system performance over time, as it becomes more challenging to find contiguous slice of memory for new processes.

While context switches are necessary for multitasking, excessive context switching can indeed lead to a significant loss of execution power by introducing overhead and resource contention in the system. 

Therefore, efficient scheduling algorithms and optimization techniques are crucial for minimizing the impact of context switches on system performance.
