---
title: Cloud
sidebar_position: 2
---


![](img/cloud_users.png)


This infrastructure represents "Layer Zero," a foundational, decentralized alternative to traditional cloud providers, designed for workloads requiring high security, scalability, and sustainability.

1. **Kubernetes Deployment**:
   - Users can deploy Kubernetes clusters directly on the ThreeFold Grid. This enables container orchestration for applications requiring high scalability and availability.

2. **Docker Container Support**:
   - Docker containers can run on the 3Nodes, providing flexibility for developers and system administrators to package, distribute, and execute applications efficiently.

3. **Virtual Machine Deployment**:
   - Users can deploy virtual machines (VMs) on the ThreeFold Grid, offering isolated and customizable environments for applications.

4. **Storage Solutions**:
   - The ThreeFold Grid provides scalable storage, with 8,124,403 GB of SSD capacity, allowing for decentralized and secure data storage solutions.

### Underlying Infrastructure

- **Interconnected 3Nodes**:
  - The 3Nodes collectively form a decentralized network that provides compute, storage, and networking capabilities.
  - The network operates using Zero OS, a self-healing planetary operating system, ensuring reliability and scalability.

### Advantages

- **Decentralized and Autonomous Cloud**:
  - The platform operates without central authority, ensuring data autonomy, security, and resilience.
- **Support for Web 2 and Web 3 Applications**:
  - Workloads can range from traditional web applications to blockchain, AI, and metaverse-related solutions.

## Dashboard

![](../img/dashboard.png)

## Get Started

- [Explore the ThreeFold Dashboard](https://dashboard.grid.tf/)
- [Read the ThreeFold Manual](https://manual.grid.tf/)