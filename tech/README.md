<h1> ThreeFold TFGrid Main Documentation Website</h1>

<h2>Table of Contents</h2>

- [Introduction](#introduction)
- [Install Hero](#install-hero)
- [Deploy Locally](#deploy-locally)
- [Push Changes](#push-changes)
- [URL](#url)

---

## Introduction

This repository contains the code to deploy the ThreeFold TFGrid Main Documentation website.

## Install Hero

```bash
curl https://raw.githubusercontent.com/freeflowuniverse/herolib/refs/heads/development/install_hero.sh > /tmp/install_hero.sh
bash /tmp/install_hero.sh
```

## Deploy Locally

You can run the website in your development environment on a local browser. Make sure to start a new shell if you've just installed <PERSON>.

```
hero docusaurus -u https://git.ourworld.tf/tfgrid/docs_tfgrid4_tech -d
```

## Push Changes

- Development
    ```
    hero docusaurus -u https://git.ourworld.tf/tfgrid/docs_tfgrid4_tech -bpd
    ```
- Production

    ```bash
    hero docusaurus -u https://git.ourworld.tf/tfgrid/docs_tfgrid4_tech -bp
    ```

## URL

- The website in staging mode is at https://dev.threefold.info/tech
- The website in production mode is at https://threefold.info/tech