---
sidebar_position: 3
---

# Architecture for an Upgraded Internet


![](../img/architecture.png)

- **3Nodes**: Deliver compute, storage, and GPU capacity.
- **Mycelium Routers**: Allow all Mycelium Network participants to communicate with each other and also connect over existing Internet links. Mycelium Routers provide bandwidth to our ecosystem.
- **WebGateways**: Provide a bridge between the current Internet and the Mycelium Network.
- **3AI 3Bots**: Represent our digital lives and possess the knowledge to act as virtual system administrators, ensuring our IT workloads remain operational.
- **Users**: Arrange their digital lives through their 3AI 3Bots.
- **AI Clouds**: Are created by connecting GPUs from the 3Nodes over the Mycelium Network.

## 3Nodes

Each 3node provides compute, storage and network capacity, its the core capacity layer of the cloud.

![](../img/3node.png)

A cloud needs hardware/servers to function. Servers of all shapes and sizes can be added. The production of Cloud Capacity is called Farming and parties who add these servers to the grid are called Farmers.

Farmers download the Zero-OS operating system and boot their servers. Once booted, these servers become 3Nodes. The 3Nodes will register themselves in a blockchain. Once registered, the capacity of the 3Nodes will become available. This enables a peer2peer environment for people or companies to reserve their Internet Capacity directly from the hardware but yet allowing full control by commercial parties if that would be required.

Each 3Node is running our Zero-OS operating system.

## Mycelium Routers

Mycelium is an end-to-end encrypted overlay meshed wireless network with agents available for any desktop and mobile operating system.

We have also created a dedicated Mycelium Router. Mycelium Routers seamlessly integrate with our Mycelium network technology, efficiently selecting the shortest path between all participants.

These Mycelium Routers are compatible not only with Satelite, Wi-Fi but also with 4G and 5G networks, ensuring versatile connectivity options.

The Mycelium Routers can be installed in locations with substantial network capacity, allowing everyone to bridge between the current Internet and the overlay Mycelium network.

## Web Gateways

The Web Gateway serves as a mechanism to connect the private (overlay) networks (Mycelium) to the open Internet.

By not providing an open and direct path into the private network, many malicious phishing and hacking attempts are stopped at the Web Gateway level for container applications.

The Web Gateways provide HTTP(S) and, in the future, other web services, which get forwarded to the relevant service exposing itself over Mycelium. This setup offers multiple access points to various backend services.


## TFChain: Our Blockchain

This blockchain does the following:

- registry for all 3bots (identity system, aka phonebook)
- registry for all farmers & 3nodes
- registry for our reputation system
- info as required for the Smart Contract for IT

This is the hart of our operational system of our decentralized cloud.


## Ultra Scalable

![](../img/architecture_scalable.png)

This architecture scales to the planet.