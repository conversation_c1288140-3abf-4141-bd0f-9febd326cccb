#!/bin/bash -ex

# Function to sync directories
sync_dir() {
    local source_dir="${HOME}/code/$1"
    local target_subdir="$2"
    local script_dir="${SCRIPT_DIR}"
    local target_dir="${SCRIPT_DIR}/docs/${target_subdir}"
    
    # Create target directory if it doesn't exist
    mkdir -p "$target_dir"
    
    # Check if source directory exists
    if [ ! -d "$source_dir" ]; then
        echo "Error: Source directory $source_dir does not exist"
        return 1
    fi
    
    echo "Syncing from $source_dir to $target_dir"
    echo "Only syncing .md, .png, .jpeg, and .jpg files"
    rsync -av --include="*.md" --include="*.png" --include="*.jpeg" --include="*.jpg" \
          --include="*/" --exclude="*" "$source_dir/" "$target_dir/"
    echo "Sync completed successfully for $source_dir to $target_dir"
    return 0
}

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

sync_dir "git.ourworld.tf/geomind/docs_geomind/collections/neurofabric/" "neurofabric/"
sync_dir "git.ourworld.tf/tfgrid/docs_tfgrid4/collections/internet_today/" "internet_today/"
sync_dir "git.ourworld.tf/tfgrid/docs_tfgrid4/collections/first_principle_thinking/" "first_principle_thinking/"

echo "All sync operations completed"