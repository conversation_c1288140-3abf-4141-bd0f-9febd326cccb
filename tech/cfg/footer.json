{"style": "dark", "links": [{"title": "Docs", "items": [{"label": "Introduction", "href": "https://docs.threefold.io/docs/introduction"}, {"label": "Litepaper", "href": "https://docs.threefold.io/docs/litepaper/"}, {"label": "Roadmap", "href": "https://docs.threefold.io/docs/roadmap"}, {"label": "Manual", "href": "https://manual.grid.tf/"}]}, {"title": "Features", "items": [{"label": "Become a Farmer", "href": "https://docs.threefold.io/docs/category/become-a-farmer"}, {"label": "Components", "href": "https://docs.threefold.io/docs/category/components"}, {"label": "Technology", "href": "https://threefold.info/tech/"}, {"label": "Tokenomics", "href": "https://docs.threefold.io/docs/tokens/tokenomics"}]}, {"title": "Web", "items": [{"label": "ThreeFold.io", "href": "https://threefold.io"}, {"label": "Dashboard", "href": "https://dashboard.grid.tf"}, {"label": "GitHub", "href": "https://github.com/threefoldtech/home"}, {"href": "https://mycelium.threefold.io/", "label": "Mycelium Network"}, {"href": "https://www2.aibox.threefold.io/", "label": "AI Box"}]}]}