---
title: License & Staking
sidebar_position: 3
---

# License & Staking

ThreeFold V4 implements license and staking mechanisms. This section explains what node vendors need to know about license and staking.

## License

The first batch of nodes are based on license fees. But the next batches will be based on staking.

Customers need to pay a license fee to use the ThreeFold Zero-OS V4 software as a certified node.

| Specs |  Fees | Currency    |
| ------------------------ |  ----- | --- |
| GB Memory             |  3     | EUR |
| GB GPU                   |  TBD   | EUR |
| TB SSD                   |  10    | EUR |
| TB HDD                   |  4     | EUR |

## Staking

The upcoming batches of nodes will be based on staking instead of license. This is yet to be determined fully but we can provide a general overview of the process.

To be recognized as a certified node, a node will need to have a given quantity of token staked on the farmer's wallet. This will allow slashing to happen if the node misbehave. It is also a way to ensure there are incentives to keep tokens within the ecosystem and thus have a great TVL (total value locked) within the ThreeFold V4 ecosystem.

Vendors will be able to stake on behalf of their farmers. This means farmers will have a smooth UX where few steps are needed to farm. In this case, it is up to the vendors to set a fair price for their nodes to compense for the staking fees.

## Vendor Considerations

As a node vendor, it's important to incorporate staking and license requirements to your products.

- Include staking or license costs in your pricing models
- Stay updated on any changes to staking and license requirements
