---
title: Bootstrap Image
sidebar_position: 4
---

# Bootstrap Image Creation

ThreeFold V4 nodes require a properly signed bootstrap image to connect to the grid. This section explains how vendors can create these images for their customers.

## Telegram Bootstrap Bot

ThreeFold provides a convenient Telegram bot that can be accessed to create signed bootstrap images of V4 Zero-OS. This automated system allows you to obtain officially signed and verified Zero-OS images This ensures a smooth onboarding process for your customers.

## Accessing the Bot

To access the bootstrap image creation bot:

1. Open Telegram and search for `@tfbootstrapbot`
2. Contact the bot admin (<PERSON>, `@scottyeager`) and ask access to the bot
3. Start the bot with the help function (`/help`)
4. Follow the prompts to generate the image
5. Download the generated image