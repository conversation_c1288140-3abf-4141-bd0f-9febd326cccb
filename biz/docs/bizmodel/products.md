---
sidebar_position: 2
title: 'ThreeFold Products'
description: 'Short overview of the products we sell'
---


## Products Overview

### Now: AI & Cloud Capacity

ThreeFold delivers critical digital infrastructure — compute, storage, and network — directly from the edge, enabling next-generation AI and Cloud use cases with built-in decentralization, sovereignty, and cost-efficiency.

#### AI Services

- Use Case: Talk to LLMs or AI Agents  
- Model: Pay-per-request  
- Revenue Potential: Scalable with demand in AI interfaces

#### Cloud Capacity

- Compute: High-availability, distributed compute nodes  
- Storage: Self-healing, decentralized storage layer  
- Network: Peer-to-peer bandwidth delivery

### Applications Layer

We enable developers and communities to build sovereign alternatives to essential apps — powered by our infrastructure.

Example Applications:

- Self-Healing Database Cluster — Cannot lose data  
- Private Chat Server — Fully encrypted, user-owned  
- Private Dropbox Alternative — Sovereign file storage  
- Notion Alternative — Decentralized knowledge base  

Like energy, our grid capacity powers everything — and over 60% of the world doesn't have this locally.

---

## Future: Web4 — Your Digital Self on the Grid

We’re building a true alternative to the internet, where every person can own and operate their digital life.

Web4 is:
- Your own backend for mail, chat, files, identity  
- Decentralized, local-first, sovereign-by-design  
- Fully integrated with the ThreeFold Grid (TFGrid)

Revenue Model Example:
- Starting at $10/month per user  
- Scales with premium features and storage  
- Community-driven marketplace will unlock more use cases

---

## Market & Value

- Reference: see our competitive pricing vs. market standards on the pricing page.
- The only active DEPIN offering this breadth of services, with a focus on:  
  - Geo-awareness  
  - Data sovereignty  
  - Sustainability  
- A $1+ Trillion market  
- Usable for Web2, Web3, and future Web4 applications  

Our goal: self-healing infrastructure, real-world deployments, and a deep commitment to decentralization.

