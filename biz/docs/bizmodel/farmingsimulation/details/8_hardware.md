---
title: Hardware Components
sidebar_position: 5
description: 'What are the components used to build a Superbrain.'
---

A Superbrain consists of 3 types of nodes.  
The setup is fully modular and can scale as demand increases.

SuperBrains can be interconnected to deliver larger capacity domains.

## NeuroMorphic AI/Quantum Nodes

![](img/ai.png)

These nodes have GPUs interconnected over a super-fast interconnect.

## Compute & Storage Capacity

![](img/compute.png)

These are compute-optimized nodes with integrated storage.

## Big GPU = Training Nodes

![](img/gpu.png)

These nodes are used for training purposes. Their financial return is lower compared to the nodes above.
