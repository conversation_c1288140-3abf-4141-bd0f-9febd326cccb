---
title: 'Deployment'
sidebar_position: 1
description: 'How is a SuperBrain Created'
---

![](img/hw.png)

The hardware setup described in the document follows a structured approach:

1. **Pods**: Each pod consists of **12 to 30 nodes** (compute/storage nodes). These nodes are integrated inside a **liquid-cooled box** to optimize thermal management and efficiency. GPU nodes take more space, compute-storage are more dense.

2. **Racks**: The pods are then placed into racks. Each rack holds multiple pods, ensuring high-density computing while maintaining optimal cooling through the liquid-cooled system.

3. **Containers**: These racks are then installed inside **datacenter containers**. Each container can accommodate up to **64 pods**, making it a modular and scalable system.

This setup ensures:
- **Efficient cooling** through liquid cooling, reducing power consumption and increasing hardware longevity.
- **Scalability** since additional pods can be added as needed.
- **Cost and power efficiency** by housing multiple pods in a single rack and then within a container.

The document outlines the costs, power usage, and licensing expenses required to maintain this setup over six years. It also details the return on investment through **cloud units (CU), SSD storage, GPU usage, and network utilization (NU).**

> [A Super brain is the extension of our Tier-S Datacenter concept as described in the following document.](https://threefold.info/datacenter/docs/tier_s/tier_s_pod)
