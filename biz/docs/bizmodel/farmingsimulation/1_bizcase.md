---
title: Business Case
sidebar_position: 1
description: 'Overview of the Business Case'
---

Our system offers two deployment modes: the SuperBrain concept, which enables highly secure and dense deployments, and the Edge Node concept, where individuals can host a compact unit at home or in the office.

While the business model primarily focuses on the SuperBrain, it's important to highlight that the Edge Node use case is equally profitable.

Our goal is to demonstrate that every farmer can benefit from a strong and sustainable business model with this system.

![](img/tiers.png)


This business case outlines the potential of the **SuperBrain** model and demonstrates why it offers a significant improvement over traditional datacenter architectures.

A SuperBrain provides raw AI, storage, and compute capacity to users with the same ease and accessibility as electricity.

Thanks to its **neuromorphic properties**, the system achieves improved efficiency, enhanced security, and greater scalability.

![](img/pl.png)

**CAPACITY PROVIDED**  

**130,000** Neuromorphic Cells 

- Compute Capacity = **65,000** Logical CPU - Cores with 2 GB of mem each.  
- SSD Storage Capacity (in GB): **4,100,000**  
- HDD Storage Capacity (in GB): **16,000,000**  
- GPU Capacity (in GPU Units of 24 GB): **707**  

## Remarks

The current model is not fully optimized, as it does not yet account for:

- The reinvestment of margins to expand capacity, which can in turn generate additional income—creating a compounding growth loop.
- The potential for increased revenue from value-added services, especially those enabled by **Web 4** technologies.

**INCA** is a digital currency that enables the SuperBrain to connect to a global network of capacity. While INCA’s price has a notable influence on the financial model, the projections remain strongly positive, even under scenarios that exclude INCA’s contribution.
