---
title: 'Intro'
sidebar_position: 0
description: 'How does it work'
---

# Understanding INCA Token Economics

This section shows you how ThreeFold's INCA token works in real-world scenarios. Think of these simulations as "what if" scenarios that show how the INCA token price naturally responds when more people start using ThreeFold's internet capacity.

## How ThreeFold Grows: The Basics

### Farmers: The Internet Builders

Farmers are regular people who add computer hardware (nodes) to build ThreeFold's new internet. 

When farmers contribute:

- They earn rewards both for making capacity available and when people use it
- Their rewards depend on practical factors like where their equipment is located and how reliably it runs
- The simulation gradually increases how much capacity gets used over time (just like a real network would grow)

Farmers need to stake (set aside) some INCA tokens:

- At the beginning, this is about 10% of what they invested in hardware
- Early farmers benefit as the token price rises, since their initial stake becomes worth more

### What Drives Usage

People use ThreeFold capacity for:
- Cloud computing (a trillion-dollar market) initially
- Web4 applications (what we believe people really need, and might be an even bigger market)

We have defined a normalized ThreeFold node which is like an average node over all markets and locations, this makes it easier to simulate.
This normalized ThreeFold node earns about $223 per month. We've checked these prices against the market to make sure they're competitive - see the [pricing comparison here](../farmingsimulation/details/6_pricing_check.md).


### Why More INCA Tokens Are Needed Over Time

As the network grows:

1. **More Revenue = More Buying**: As farmers earn more, they need to buy more INCA with regular money
2. **More Staking Required**: Both validators and farmers need to lock up more tokens as the network expands
3. **Limited Supply**: There simply aren't enough INCA tokens to go around, creating natural buying pressure

### The Internet of Internets

We're not building one massive network. 

Instead:

- We're creating thousands of smaller networks called "Internet Zones"
- Each Zone can support up to 10,000 users (though they could handle millions)
- Each Zone needs validators (special nodes that verify transactions)
- Validators earn a percentage of revenue and help people move money in and out of the network
- They also run our L3 blockchain, letting people use regular money or other digital currencies

Our models show that as the grid grows, the INCA token naturally needs to increase in value. We've also created [a simulation](whatif/inca_100k.md) showing what happens if there aren't enough INCA tokens available (spoiler: it doesn't work).

## Are These Numbers Realistic?

- We've modeled between 100,000 and 1,000,000 nodes - just a small fraction of what a trillion-dollar market would need
- 100,000 nodes worldwide is actually quite modest - especially with the government and telecom projects we're working on
- For perspective, the global cloud market is worth a trillion dollars - see our [market analysis](../farmingsimulation/details/50_market.md)

## How Our Simulation Works

Our model tracks:

1. **Token Supply**: The fixed maximum number of INCA tokens and when they become available
2. **Network Growth**: How many nodes join the network over time
3. **Token Demand**: How many INCA tokens are needed for the network to function
4. **Price Balance**: The price where supply and demand reach equilibrium
5. **Validator Economics**: How much validators can earn

Important: This simulation focuses on real usage, not speculation. It shows how the token value grows based on actual network growth and usage.

**The key insight: As more people use the network, there simply aren't enough INCA tokens to go around at lower prices. The price must increase to balance limited supply with growing demand.**

## Why This Works: Plain and Simple

1. **Limited Supply**  
   There's a fixed number of INCA tokens. As more people join, each person needs some tokens, but there's only so many to go around.

2. **Growing Demand**  
   As more people use ThreeFold services, they all need tokens. If the price stayed low, we'd quickly run out.

3. **Basic Economics**  
   When more people want something than there is available, the price goes up. It's like concert tickets for a popular show - when they're scarce, the price rises.

4. **Natural Price Adjustment**  
   If more people want to buy tokens than there are available, they'll pay more to get them. A higher price means each token can represent more value, so fewer tokens can support more activity.

5. **Network Growth Effect**  
   As more nodes join and more services run, more tokens get locked up or used. This further reduces the available supply, pushing prices up more.

6. **The Bottom Line**  
   For the network to grow successfully, INCA tokens must increase in value so that the limited number of tokens can support all the activity happening on the network.
