---
title: 'Not Enough INCA'
sidebar_position: 3
description: 'INCA at 0.5 USD'
---

## INCA token at 0.5

Which is still 5x launching price.

![](img/sim_100k_not.png)

**Why the Model “Breaks” at a Token Price of 0.5**

1. **Usage Drives the Grid**  
   - The network’s services and nodes attract users from outside the INCA ecosystem. These users need to acquire INCA to access or run these services.  
   - As usage grows, the total demand for INCA rises in direct proportion to the number of new participants and their required stakes or fees.

2. **External Buyers Must Purchase INCA**  
   - Because new users come from outside the token system, they must buy INCA with external currencies (e.g., USD, EUR).  
   - This external influx of capital puts additional buying pressure on INCA.

3. **Limited Token Supply**  
   - The model shows that there isn’t enough INCA available at a price of 0.5 to meet all the incoming demand.  
   - With tokens locked up in node staking and other uses, the circulating supply remains too low to accommodate a large wave of external buyers.

4. **Price Must Increase to Reach Equilibrium**  
   - When too many buyers chase too few tokens, the price is naturally pushed up until supply and demand balance out.  
   - At 0.5, the model indicates a fundamental shortfall of tokens; therefore, the market would adjust by raising the INCA price so that fewer tokens are needed to represent the same total value.

**In summary,** at a fixed token price of 0.5, the demand from outside users overwhelms the available token supply, creating a market imbalance. The only way to resolve this is for the token price to rise, ensuring enough total value is in the system to support the network’s usage and reward mechanisms.


> [original simulation do see here](https://docs.google.com/spreadsheets/d/1rFU4iE87raxhMQFe4_VnYWSNDRuVhAvRWGJqEAk2Sg0/edit?usp=sharing)
