---
title: 'Simulation'
sidebar_position: 1
description: 'Simulation for INCA at 5 USD'
---


## INCA token at 5 USD

![alt text](img/growth500k.png)

> Which is  100x launching price.

![](img/sim_500k.png)

> Note that the revenue is too high still in relation to token price, this model probably asks for a higher token price.

**The model works at a Token Price of 5**

1. **Enough Value in the System**  
   - At **5 USD per INCA**, the total value in circulation is finally high enough to cover the network’s projected usage and staking needs.  
   - Unlike the lower price scenarios the model no longer shows shortfalls or negative balances in the “red cells.”

2. **How Many Nodes Are in the Field**  
   - The spreadsheet calculations indicate that at **5 USD**, a certain number of nodes (**1.7 Million** nodes) can be fully supported—i.e., each node’s required stake and rewards are properly funded, this nr is only after 10 Years.  
   - This **1.7 Million** figure (visible in the Node Expanded rows/columns of your sheet) represents the actual number of active nodes the network can sustain at 5 USD per token. This is still not  very big number compared to total market size. 

3. **Still Much Lower Than Market Demand**  
   - The overall market potential for nodes is significantly higher—possibly.
   - In other words, there are still far more potential participants who want to operate nodes than the system is currently accommodating.  
   - This gap suggests that if demand keeps rising, the token price may need to climb even further to bring enough total value into the ecosystem to support more nodes.


## Details


## 1. Overall Purpose

- **Goal**: Simulate the growth of a decentralized cloud network from zero up to 500,000 nodes over four years (16 quarters).  
- **Key Focus**:  
  - How many nodes are added each quarter (and cumulatively).  
  - How many INCA token are generated and staked. 
  - The revenue generated from the activities on the ThreeFold Grid (Consumption of Internet capacity, money transaction in & out)
  - Increase in the price of INCA. 

## 2. Inputs and Assumptions

 **INCA Price in USD**  
   - We have assumed a sustainable increase in the price of the INCA over 10 years (from 0.1 in Q1 to 5 at the end of Y10). For instance: 
      - By the end of Y1, we will reach an INCA price of 1 USD 
      - By the end of Y4, we will reach an INCA price of 4.3 USD



## 3. Quarter-by-Quarter Calculations

1. **Node Sales**  
   - The sheet shows how many new nodes are sold (in thousands) each month, then sums them up per quarter. 
   - For example, in Q1 we assume that 200 nodes per month will be sold, Q2  to 2000, Q3 to 6000, etc.
   - We assume that:
      - By end of Y1, we will reach 56,000 Nodes
      - By end of Y2, we will reach 191,000 Nodes
      - By end of Y4, we will reach 506,000 Nodes, etc

2. **Revenue Calculations Section**  

Below is the total revenue generated on the ThreeFold Grid: 

   - We calculate the Total USD Revenue generated by the total amount of nodes per month ("A"): 
      - A percentage utilization rate per the node available on the ThreeFold Grid (Q1 = 15%, Q4 = 30%, Y3 and Y4 = 80%).
      - From this internet capacity utilization, we assume that part of it is from the Cloud.
      - We then calculate average revenue per node per month. 223 USD is the income generated per month per normalized node. 
      - From there, we are able to calculate the Total Revenue for the total amount of nodes. 
   
   - We then calcualate the Total USD Revenue per month according to the total amount of users by ("B"): 
      - Estimating the total amount of Users on the ThreeFold Grid (We assume that 40 is the number of User per node). We remove the percentage of user using the Cloud capabilities.
      - We then calculate the total amount of users using other internet capacity other than for Cloud usage according to the total number of Nodes. For instance, we calculated that by Q5, the total number of users is 180,000 and end of Y4.
      - We assume the average spending per user on the ThreeFold Grid (Starting with 20 USD per user per month to 40 USD by the end of Year 10)
      - From there, the total spending according to the total number of users is calculated. 
   
   - The total revenue generated as per A and B is then calculated in INCA according to the INCA price per period. 


3. **Validator Calculator**

   - Validator Revenue generated from transaction on the Network: 
      - We calculated the number of Nodes per Zone.
      - We assume starting with a total zone of 1 in Q1, scaling to 21 in Q8 and 56 at the end of Y4. 
      - We assume that we will start with 9 Validators per zone in Q1 scaling up to 10 per zone by the end of Y4. 
      - Validators takes a commission of 1% on each transaction. Transactions are money going in and out of the network.  
      - We estimate an average transaction amount of 40 USD per user per month which will be scaled up to 50 USD by Year 10. The total transaction is calculated and estimated. 
      - We calculate the profit per Validator from the transactions (By the end of Y4, Validator will make 7,892 USD per month)
   
   - Validator Revenue generated from using Internet Capacity: 
      - Validator takes a commission of 5% per internet capacity consumption/purchase
      - We then calculate a 5% commission on total revenue generated on the ThreeFold Grid. A validator is expected to make 3,416 USD per month by Y3 and 3,810 USD by Y4. 
   
   - We then calculate the total revenue per validator per month from transaction on the network and usage of internet capacity. 


4. **INCA Required for Staking**  
   - In order to operate a Validator, INCA needs to be staked. We estimated that at start 50,000 USD worth of INCA per validator will have to be staked. At a price of 0.1 USD, as in Q1 we estimate to have a total of 9 validators, we forecasted a total amount of 5M INCA to be staked. This will scale up to 25m INCA by Y4 with a total validator of 562. 

   - By acquiring a node, the Farmer (purchaser) will have to stake 20% of the value of the investment in INCA. 
   - We calculate the total amount of INCA staked according to the total amount of nodes being sold. 




> [original simulation do see here](https://docs.google.com/spreadsheets/d/1rFU4iE87raxhMQFe4_VnYWSNDRuVhAvRWGJqEAk2Sg0/edit?usp=sharing)
