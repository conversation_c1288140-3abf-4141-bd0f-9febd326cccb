# Docusaurus HeroScript Generator

## Task
Analyze the provided documentation package and generate a complete HeroScript configuration for Docusaurus that matches the project's structure, content, and requirements.

## Input
- The documentation folder structure
- Any existing configuration files
- README files or other project metadata
- The project's theme, branding, and style guidelines (if available)

## Output Requirements
Generate a complete HeroScript file with the following sections:

1. Basic configuration (!!docusaurus.config)
2. Metadata configuration (!!docusaurus.config_meta)
3. Navigation structure (!!docusaurus.navbar and !!docusaurus.navbar_item)
4. Sidebar configuration based on the docs structure
5. Import sources configuration (if applicable)
6. Theme customization (if needed)

## HeroScript Format Reference

HeroScript uses a simple declarative syntax where each action starts with `!!` followed by the actor and action name. Parameters use a `key: value` format.

Example:
```heroscript
!!docusaurus.config
    name:"project-docs"
    title:"Project Documentation"
    tagline:"Clear, comprehensive documentation for developers"
    url:"https://docs.example.com"
    url_home:"docs/"
    base_url:"/"
    favicon:"img/favicon.png"
    copyright:"© 2023 Project Team"
```

## Docusaurus Information

Docusaurus is a modern static website generator focused on documentation sites. Key features:

- **Content-centric**: Optimized for documentation with Markdown support
- **Versioning**: Support for multiple versions of documentation
- **Search**: Built-in search functionality
- **Internationalization**: Multi-language support
- **Theming**: Customizable themes with dark mode support
- **React-based**: Uses React for UI components

The HeroScript implementation for Docusaurus handles:
1. Site configuration and metadata
2. Navigation structure (navbar, sidebar)
3. Content import from various sources
4. Build and deployment settings
5. Theme customization

## Available HeroScript Commands for Docusaurus

### Basic Configuration
- `!!docusaurus.config`: Core site settings (name, title, URL, etc.)
- `!!docusaurus.config_meta`: SEO metadata

### Navigation
- `!!docusaurus.navbar`: Navbar configuration
- `!!docusaurus.navbar_item`: Individual navbar items
- `!!docusaurus.sidebar`: Sidebar configuration
- `!!docusaurus.sidebar_item`: Individual sidebar items/categories

### Content
- `!!docusaurus.import_source`: Import documentation from external sources
- `!!docusaurus.page`: Create static pages

### Deployment
- `!!docusaurus.ssh_connection`: SSH connection for deployment
- `!!docusaurus.build_dest`: Deployment destination

### Theme
- `!!docusaurus.theme`: Theme customization settings
- `!!docusaurus.css`: Custom CSS settings

Please analyze the provided documentation and generate a complete HeroScript configuration that best represents the project's documentation needs.