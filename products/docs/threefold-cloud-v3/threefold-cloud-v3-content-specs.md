---
title: Technical Specifications
sidebar_position: 1
description: ThreeFold Cloud V3 Content Specs
---

## Overview
ThreeFold Grid 3.x is designed as a best-effort, fully open-source infrastructure for doers, hackers, and thinkers. It follows a simplified approach with focused features.

## Version 3.16

### Major Features
- **Solana Bridge Integration** 
  - Fully operational on TF Connect
  - One-way transfer from Stellar to Solana

- **V4 Farm Creation** 
  - Available through TF Connect interface

- **GPU Enhancements**
  - Fixes and performance improvements on Dashboard
  - Support for Nvidia 4090 from partner vendors

- **AI Integration**
  - Open WebUI AI Chat accessible through Dashboard

## Version 3.17

### Documentation Reorganization
- **New Structure**
  - **manual.grid.tf/user**: Basic user documentation (VM, Mycelium Web GW, WireGuard, HeroScript)
  - **manual.grid.tf/farmer**: Core farmer documentation
  - **manual.grid.tf/labs**: Advanced documentation for users and farmers

### Dashboard Improvements
- **Enhanced UX/UI**
  - Clear distinction between VM and Labs environments
  
- **VM Section**
  - VM
  - Network: Mycelium, Web GW, WireGuard
  - Deployment: Dashboard UI and Heroscript deployments
  - Production-ready with official support
  
- **Labs Section**
  - Subsections: Apps and Orchestrators
  - Experimental features
  - All services operate with Mycelium as minimum requirement
  - Non-Heroscript IAC options
  - Best effort, open-source, no formal support

### ZOS Reorganization
- Deprecation of ZOSv3
- Focus on ZOS Light with WireGuard integration
- Quality assurance for ZOS Light performance

### Gateway Enhancements
- Minimum of 3 gateways per main region
- Improved reliability and access

### Solana Bridge
- Increased liquidity
- Clear documentation on one-way transfer from Stellar to Solana

### V4 Node Vendors
- Transparent reward structure on vendor websites
- Official ThreeFold links to vendor websites
- Vendor access to internal documentation

### Mycelium Improvements
- **Chat Integration**
- **Automatic Updates**
- **DNS Integration**
- **Enhanced Scaling**
- **Debug Tools**
- **Improved User Experience**

## Version 3.18

### GeoChain Integration
- **Base Chain Considerations**
  - Potential Cosmos infrastructure
  - Support for synthetic tokens, identity, and e-commerce

- **Payment Enhancements**
  - BTC integration as synthetic token
  - All products/services priced in μBTC (1/1,000,000 BTC)

- **Feature Additions**
  - Name services (DNS records) added to GeoChain
  - Mycelium integration with GeoChain

### TF Connect Updates
- Integration with GeoChain
- μBTC payment support in TF Connect wallet
- One-way transfer from Stellar to Solana
- V4 Farm Creation