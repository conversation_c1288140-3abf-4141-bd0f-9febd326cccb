---
title: Technical Specifications
sidebar_position: 1
description: Mycelium Specs
---

## Core Components

### Mycelium Client
- **Purpose**: Acts as a client interface to network services
- **Lead Developer**: Lee

#### Core Features
- **Peer-to-Peer VPN**
  - Shortest path routing algorithm
  - Decentralized connection architecture

- **Name Services (DNS)**
  - Records sourced from <PERSON><PERSON><PERSON><PERSON><PERSON> (explorer)
  - Local caching in Mycelium
  - Load-balanced DNS resolution
  - Fallback to internet resolution for unknown names

- **Message Bus**
  - Distributed messaging framework
  - Real-time communication capabilities

- **HTTP Server**
  - Built on flist technology
  - Accessible via DNS names
  - Supports static content hosting

### Co-Daemon
- **Lead Developers**: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Egypt team
- **Features**:
  - **Productivity Suite Support**
    - IMAP/WebDAV/CalDAV/CardDAV integration (Hero v1)

  - **AI Capabilities**
    - LLM AI router (decentralized OpenRouter alternative)
    - Voice-to-Text conversion
    - Text-to-Voice synthesis
    - Video voice extraction support

  - **Storage Services**
    - IPFS and/or FLIST storage integration
    - Docusaurus to Flist service
    - Flist to HTTP(S) service for static content hosting

## Billable Services

- **DNS Names**: Custom domain registration and management
- **AI Requests**: Usage-based billing for AI processing
- **Bandwidth**: Future implementation, not available at launch
- **Hosted Storage**: Pay-per-use storage solutions
- **Text-to-Voice Minutes**: Conversion time billing

## Decentralized AI Router

- **User Participation Model**:
  - Users can provide their OpenRouter, OpenAI, or other keys
  - 10% margin offered to participating users
  - Custom pricing options available
  - Self-routing to personal accounts

- **Host Configuration**:
  - Customizable pricing for AI service delivery
  - Open marketplace for AI processing

## User Interface

- Modern, visually appealing design
- Responsive layout for desktop and mobile devices
- Intuitive navigation
- Accessible debug information
- Real-time status indicators

## Debugging Capabilities

- Multi-level logging system
- Network traffic monitoring
- Detailed connection statistics (latency, packet loss, throughput)
- Advanced configuration options for power users

## Scalability Architecture

- Edge and public node implementation
- Segmented design with dedicated public nodes per segment
- Optimized for:
  - Low latency operations
  - Local data processing
  - Large concurrent connection handling
- Comprehensive deployment documentation

## DNS Integration

- Utilizes public servers as DNS resolvers
- Domain structure: `domain.sub1.mycelium`
- Supports Mycelium subdomain resolution
- Fallback to Google DNS (*******) for non-Mycelium domains

## Automatic Updates

- Background update checking
- User notifications for available updates
- Minimal-interaction installation process
- Optional rollback capability to previous versions

## Integrated Chat

- Built into desktop and mobile clients
- Backend powered by Vlang binary
- Core messaging features:
  - Text messaging
  - User presence indication
  - Direct user communication

## Project Timeline

- **Beta Release**: 2-3 months for most features
- **Project Owner**: Kristof
- **Development Team**: Timur, Kristof, Mahmoud, Lee, Maxim, Jan, and additional BE team members
