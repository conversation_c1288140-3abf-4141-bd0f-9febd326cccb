---
title: Technical Specifications
sidebar_position: 1
description: ThreeFold Cloud V4 Content Specs
---

## Overview
ThreeFold Grid 4.x represents the next generation of the decentralized grid infrastructure, featuring enhanced capabilities and deeper integration with blockchain technology.

## Alpha 2

### Core Development Focus
- **ZOS v4 Stability**
  - Improved reliability and performance
  - Robust testing and quality assurance

- **Database Infrastructure**
  - Implementation of reliable, properly backed-up database

- **Usage Limitations**
  - Developer-only access
  - End users restricted from running workloads during Alpha 2

### ZOS v4 Specifications

#### Installation Capabilities
- Installable on bare metal hardware
- Installation process for <PERSON>tz<PERSON>
- Installation process for OVH

#### Node Registration
- Centralized tool for node registration
- Comprehensive node registrar
- Daily backup system for registrar

#### Dashboard Integration
- ZOS4 nodes visibility in existing dashboard
- Restricted deployment access for farmers/users

#### Rewards & Monitoring
- Database update system for rewards
- Statistics and metrics visibility
- Grafana + Prometheus monitoring integration

#### Staking for Farmers
- Implementation of staking mechanism for farmers
- DIY farmers can stake tokens directly for their own nodes
- Hardware vendors can stake on behalf of their customers

#### Management & Security
- SSH access for trusted developers
- Complete resource access for developers
- Remote upgrade and reboot capabilities

#### Documentation
- Simplified developer documentation

## Alpha 3: GeoChain Integration

### Hardware Vendor Integration
- Nodes can be sold through GeoChain 4
- Wallet visibility for hardware
- 10% revenue share to ThreeFold

### Referral System
- Built-in referral tracking in GeoChain
- Transparent reward distribution

### INCA Token Management
- Private token management
- Not available on public chains

### Name Services
- DNS records integrated into GeoChain
- Enhanced domain management

### Mycelium DNS Enhancements
- DNS server in Mycelium pulling information from GeoChain
- Load-balanced architecture
- Geo-aware resolution for optimal performance

---

*Note: All specifications are subject to change as development progresses.*
