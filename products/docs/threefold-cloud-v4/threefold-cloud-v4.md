---
title: Product Presentation
sidebar_position: 0
description: Introducing ThreeFold Cloud V4
---

## The Future of Decentralized Computing

ThreeFold Cloud V4 represents the next evolution in decentralized cloud computing. This new version delivers enhanced performance, expanded capabilities, and improved economic models that will benefit the entire ecosystem.

![threefold_cloud](../img/become_farmer.png)

## For Developers & Farmers

ThreeFold Cloud V4 is currently in Alpha phase, designed for ThreeFold developers to test the decentralized technologies. Farmers can already participate by acquiring new V4 nodes or upgrading existing infrastructure to V4 specifications.

## What's New in V4

### Geochain Integration
- **Decentralized Governance**: Built on Cosmos SDK for robust consensus
- **Native Payments**: Support for Bitcoin-pegged tokens
- **Name Services**: Integrated DNS and naming system
- **Hardware Marketplace**: Purchase nodes directly through the chain

### Advanced Node Architecture
- **Improved ZOS**: More efficient workload management
- **Enhanced Reliability**: Better backup and recovery systems
- **Resource Optimization**: More effective utilization of hardware
- **Expanded Hardware Support**: Better GPU integration and specialized hardware

### Economic Improvements
- **Bitcoin Integration**: Pricing and payments in micro-Bitcoin (uBTC)
- **Hardware Vendor Programs**: Purchase certified hardware from partners
- **Referral Systems**: Earn rewards for growing the network
- **Simplified Token Economics**: More intuitive value exchange
- **Token Staking for Farmers**: To be in line with the DePin industry

## How to Participate

ThreeFold Cloud V4 needs farmers to provide the infrastructure:

- **Purchase Certified V4 Nodes**: Buy new pre-configured hardware directly from our certified partners
- **Build DIY V4 Nodes**: Set up your own hardware following our V4 specifications
- **Migrate Existing Hardware**: Move your current certified or DIY V3 nodes to the V4 network

While deployment capabilities are currently limited to ThreeFold developers during this Alpha phase, farmers are essential to building the infrastructure that will power the V4 network.

[Pre-order ThreeFold v4 nodes →](https://docs.threefold.io/docs/become-a-farmer/get_started)
