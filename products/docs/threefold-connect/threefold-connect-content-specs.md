---
title: Technical Specifications
sidebar_position: 1
description: ThreeFold Connect App Content Specs
---

## Core Components

### ThreeFold Connect App

#### Identity & Authentication
- **Secure Digital Identity**
  - Unique human-readable ThreeFold ID
  - Simplified interactions using IDs instead of wallet addresses
  - Universal recognition across all ThreeFold services
  - QR-based Dashboard desktop session linking
  - Secure seed phrase for each account
  - Enhanced user experience by hiding cryptographic complexity

#### Mycelium Network Integration
- **Peer-to-Peer Connectivity**
  - Native Mycelium client built directly into app
  - End-to-end encrypted communications
  - Shortest path routing algorithm
  - Decentralized name resolution system
  - Connect to Mycelium users via the ThreeFold ID

- **Integrated Services**
  - Secure messaging platform
  - Content publication capabilities
  - File sharing functionality
  - AI service routing and access

- **Network Management**
  - Connection status monitoring
  - Bandwidth and latency optimization
  - Service discovery interface
  - DNS resolution for human-readable names

#### Community Integration
- **Forum Access**
  - Direct integration with ThreeFold Forum
  - Notification system for responses
  - Thread subscription management

- **Information Hub**
  - Blog access with categorized content
  - Newsroom integration with filterable updates
  - Structured link trees for ecosystem navigation:
    - Hardware vendor websites
    - Documentation repositories:
      - manual.grid.tf/user (For basic users)
      - manual.grid.tf/farmer (For basic farmers)
      - manual.grid.tf/labs (For advanced users/farmers)

#### Governance Features
- **DAO Participation**
  - Token-based voting system
  - Node-based voting system
  - Vote tracking and results viewing

#### Token Management
- **Complete Wallet Control**
  - Buy/sell/swap functionalities
  - Send/receive functionalities
  - Transactions based on ThreeFold ID (human-readable) or wallet address
  - Real-time portfolio tracking and price monitoring
  - Transaction history and status management

- **Multi-Chain Bridge Support**
  - Stellar network support
  - Stellar-TFChain bridge and integration
  - Stellar-Binance Smart Chain (BSC) bridge
  - Stellar-Ethereum bridge
  - Stellar-Solana bridge (one way only)
  - Cross-chain transaction history

- **Fiat On/Off Ramps**
  - MoonPay integration for credit/debit purchases
  - MoonGram support for cash-based purchases
  - Automatic bridging from XLM to TFT
  - Transaction tracking and receipt storage

### User-Specific Features

#### Simplified Application Deployment
- **Ready-to-Use Applications**
  - Jitsi video conferencing
    - Room creation and management
    - Shareable access links
    - Configuration options

  - OpenWebUI
    - AI chat interface
    - Model selection options
    - Conversation history

  - Nextcloud
    - Full application deployment
    - Storage allocation management
    - User account administration

- **Backup Solutions**
  - Automated database backup configuration
  - Scheduled backup options
  - Restore capabilities

- **Hero AI Integration**
  - Natural language processing for deployment requests
  - Voice-to-text conversion
  - Text-to-HeroScript transformation
  - Deployment execution

#### User Support
- **Dedicated User Channels**
  - User-specific support ticket creation
  - Ticket categorization by application type

### Farmer-Specific Features

#### Farm Administration
- **Farm Setup & Management**
  - V3 and V4 farm creation interface
  - Farm management system (IP address, additional price, etc.)
  - V4 staking interface and tracking

- **Monitoring Tools**
  - Real-time uptime tracking
  - Alert system with customizable thresholds
  - Reward calculation and projection
  - Historical performance metrics

- **Farmer Support**
  - Farmer-specific support ticket creation
  - Ticket categorization by issue type

#### Node Bootstrapping
- **DIY Node Deployment**
  - Bootstrap image generator (in-app and website versions)
  - Email delivery of bootstrap links
  - Customized URLs with farm ID integration
  - Platform-specific image options (UEFI/BIOS)

- **Certified Hardware**
  - Vendor website integration for purchases
  - Farm ID inclusion in ordering process
  - Automatic node registration

- **Node Requirements**
  - UEFI/BIOS management specifications
  - Network specifications (DHCP)
  - Uptime and bandwidth specifications

#### Farmer Support
- **Dedicated Farmer Channels**
  - Farmer-specific support ticket creation
  - Ticket categorization by farming issue type

### ThreeFold Dashboard Integration

#### Authentication System
- **QR Code Session Linking**
  - Temporary session key generation
  - Mobile-to-desktop secure authentication
  - Session management and expiration

#### Section Organization
- **VM Environment** (Basic Farmers)
  - VM management interface
  - Mycelium network configuration
  - Gateway setup and administration
  - WireGuard VPN management

- **Labs Environment** (Advanced Users)
  - Experimental application deployment
  - Orchestrator management tools
  - Advanced configuration options

#### Payment Integration
- **Seamless Mobile Authentication**
  - QR-based payment approval system
  - Transaction verification via TF Connect
  - Payment history synchronization

---

*Note: All specifications subject to change as development progresses.*
