---
title: Technical Specifications
sidebar_position: 1
description: AIBox Specs
---

## Product Overview

AIBox is a specialized 3node hardware and product offering designed for AI computing enthusiasts and professionals. It provides on-premises GPU power combined with decentralized grid integration, creating a unique hybrid solution that allows users to both use and monetize their AI computing hardware.

## Hardware Configurations

### Chassis Options
All versions use the same high-quality chassis with the following specifications:

| Component | Specification |
|-----------|---------------|
| CPU | AMD high-performance processor |
| Memory | 64 GB RAM |
| Storage | 2 × 2 TB SSD |
| Graphics | AMD 7800 XTX GPUs (variable) |
| Cooling | Water-cooled GPU/CPU for noise reduction |
| Power Supply | High-quality PSU |
| Optional | Integrated mini computer with relay for remote management |

### Model Variants

| Model | GPUs | Approximate Price |
|-------|------|------------------|
| Base AIBox | 0 GPU | $1,000 |
| 1GPU AIBox | 1 GPU | $3,000 |
| 2GPU AIBox | 2 GPU | $5,000 |

## Staking & Financials

- **INCA Staking**: 10% of sales price automatically staked in INCA tokens
- **Hardware Channel**: Margin for hardware distribution partners (percentage TBD)
- **Revenue Split**: 
  - 80% to box owner
  - 10% to farming pool
  - 5% to validators
  - 5% to ThreeFold

## Capabilities

### AI Model Hosting
- Run AI models directly on local GPU hardware
- Expose models securely over Mycelium network
- Owner control over which models to allow
- Option for owner to reserve 100% of GPU capacity as needed

### Virtualization
- Cloud slice functionality with up to 8 VMs per device
- Owner controls GPU allocation per slice (0, 1, or 2 GPUs)
- No GPU oversubscription for optimal performance
- Flexible GPU connect/disconnect for users (when permitted)

### OS & Container Support
- Minimal OS with Docker/Podman support
- Pre-configured Docker images for common AI tasks
- Support for native containers and flist technology
- Exclusive Mycelium network connectivity for enhanced security

### External Connectivity
- Web gateways for external access to VM containers
- Secure exposure of services through Mycelium network

## Management

- **Geochain**: Primary management tool for AIBox delivery and operations
- **Remote Administration**: Optional serial management interface for advanced control

## Billing & Tokens

- **Primary Currency**: Bitcoin (BTC)
- **AIToken**: Native token in TFChain 3 ecosystem
- **Conversion**: Bi-directional AIToken-BTC exchange at 1 μBTC rate (subject to liquidity)
- **TFGrid Integration**: TFT purchased at 1 μBTC when using TFGrid 3 capacity

## Value Proposition

### For Users
- Local ownership of AI GPU hardware
- Investment recovery through capacity rental
- Full control over hardware usage and allocation
- Private AI computation without reliance on cloud providers

### Differentiators
- Unique investment recovery model
- Integration with decentralized grid technologies
- Ability to monetize spare capacity
- Balance of ownership and sharing economy

## Go-to-Market Strategy

- Referral-based marketing program
- Hardware channel partnerships
- AI community engagement
- Influencer demonstrations and promotion

## Development Team

- **Project Owner**: Kristof
- **Core Team**: Timur, Kristof, Mahmoud, and additional members TBD
- **Business Development**:
  - 1 Lead (also responsible for growth hacking)
  - 1 Growth Hacker (demos, influencer outreach)
  - 1 Channel Manager (hardware partnerships)

### Financial Support for Business Deelopment

- 1-3K monthly budget per role
- Team compensation includes INCA token salary component

## Timeline

- Beta release of most features expected within 2 months

---

*Note: All specifications subject to change as the product evolves.*