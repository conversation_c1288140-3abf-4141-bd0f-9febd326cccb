---
title: Technical Specifications
sidebar_position: 1
description: Educational Content Specs
---

## Core Components

### Content Library
- **10+ Million Educational Resources**
  - Comprehensive collection of learning materials
  - Multi-format content (video, text, interactive)
  - Accessible via decentralized delivery network

### Integration Partners
- **Sikana**
  - Practical skills-focused library
  - Hands-on tutorial approach
  - Community-contributed content
  - Offline access capabilities

- **Wizenoze**
  - AI-powered educational content curation
  - Curriculum matching technology
  - Reading level classification system
  - Personalized learning pathways

### Technical Infrastructure
- **Built on ThreeFold Technology**
  - Runs on ThreeFold Grid
  - High availability architecture (no SPOF)
  - Accessible via:
    - Mycelium network
    - Web gateway
  - AI Box integration for advanced features

## Sikana Platform Features

### For Learners
- **Diverse Topic Library**
  - Practical everyday skills
  - Hands-on demonstrations
  - Step-by-step guided learning

- **Accessibility Features**
  - Offline content availability
  - Mobile-friendly design
  - Low bandwidth optimization

### Content Attributes
- **Multi-format Resources**
  - Video tutorials
  - Interactive exercises
  - Project-based learning
  - Community forums

## Wizenoze Integration

### Content Enhancement
- **AI-Driven Curation**
  - Content matched to curriculum requirements
  - Age-appropriate material selection
  - Reading level classification

### Target Segments
- **K-12 Education**
  - Curriculum-aligned resources
  - Age-appropriate content filtering

- **Vocational & Career Training**
  - Industry-specific materials
  - Practical skill development

- **Educational Publishers**
  - Content enrichment tools
  - Curriculum mapping services

### Technology Features
- **API Integration**
  - Seamless platform connectivity
  - Custom content delivery
  - Analytics and reporting tools

## Online School Platform

### Educator Tools
- **Complete LMS System**
  - Based on Open edX technology
  - Course creation toolset
  - Student management system

- **Monetization Options**
  - Custom pricing models
  - Subscription management
  - Payment processing integration

- **Branding & Customization**
  - White-label capabilities
  - Custom domain integration
  - Branded certificates

### Learning Features
- **Assessment Tools**
  - Quiz and test creation
  - Assignment management
  - Progress tracking

- **Community Building**
  - Discussion forums
  - Collaborative workspaces
  - Peer review systems

- **Analytics Dashboard**
  - Student engagement metrics
  - Completion rate tracking
  - Performance analytics

## Technical Specifications

### Infrastructure
- **Decentralized Architecture**
  - Grid-based deployment
  - Distributed content delivery
  - Data sovereignty protection

- **Scalability**
  - Automatic resource allocation
  - Load balancing
  - On-demand scaling

### Security & Reliability
- **High Availability Design**
  - Redundant systems
  - No single point of failure
  - Automatic failover

- **Data Protection**
  - End-to-end encryption
  - Privacy-first design
  - Compliant with educational data standards
