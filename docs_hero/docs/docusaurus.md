---
title: Docusaurus
sidebar_position: 3
---

## Using Docusaurus

Once you've set up Hero, you can use it to develop, manage and publish Docusaurus websites.

## Launch the Hero Website

To start a Hero Docusaurus website in development mode:

- Build the book then close the prompt with `Ctrl+C`
  ```bash
  hero docusaurus -d
  ```
- See the book on the local browser
  ```
  bash /root/hero/var/docusaurus/develop.sh
  ```

You can then view the website in your browser at `https://localhost:3100`.

## Publish a Website

- To build and publish a Hero website:
  - Development
    ```bash
    hero docusaurus -bpd
    ```
  - Production
    ```bash
    hero docusaurus -bp
    ```

If you want to specify a different SSH key, use `-dk`:

```bash
hero docusaurus -bpd -dk ~/.ssh/id_ed25519
```

> Note: The container handles the SSH agent and key management automatically on startup, so in most cases, you won't need to manually specify keys.