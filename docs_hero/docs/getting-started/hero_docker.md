---
title: Build with <PERSON><PERSON>
sidebar_position: 2
---

You can build Hero as a Docker container.

The code is availabe at this [open-source repository](https://github.com/mik-tf/hero-container).

## Prerequisites

- Docker installed on your system (More info [here](https://manual.grid.tf/documentation/system_administrators/computer_it_basics/docker_basics.html#install-docker-desktop-and-docker-engine))
- SSH keys for deploying Hero websites (if publishing)

## Build the Image

- Clone the repository
    ```
    git clone https://github.com/mik-tf/hero-container
    cd hero-container
    ```
- Build the Docker image:
    ```bash
    docker build -t heroc .
    ```

## Pull the Image from Docker Hub

If you don't want to build the image, you can pull it from Docker Hub.

```
docker pull logismosis/heroc
```

In this case, use `logismosi/heroc` instead of `heroc` to use the container.

## Run the Hero Container

You can run the container with an interactive shell:

```bash
docker run -it heroc
```

You can run the container with an interactive shell, while setting the host as your local network, mounting your current directory as the workspace and adding your SSH keys:

```bash
docker run --network=host \
  -v $(pwd):/workspace \
  -v ~/.ssh:/root/ssh \
  -it heroc
```

By default, the container will:
- Start Redis server in the background
- Copy your SSH keys to the proper location
- Initialize the SSH agent
- Add your default SSH key (`id_ed25519`)

To use a different SSH key, specify it with the KEY environment variable (e.g. `KEY=id_ed25519`):

```bash
docker run --network=host \
  -v $(pwd):/workspace \
  -v ~/.ssh:/root/ssh \
  -e KEY=your_custom_key_name \
  -it heroc
```
