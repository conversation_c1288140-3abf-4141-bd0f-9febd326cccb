---
title: Build Natively
sidebar_position: 1
---

## Basic Hero

You can build <PERSON> natively with the following lines:

```
curl https://raw.githubusercontent.com/freeflowuniverse/herolib/refs/heads/development/install_hero.sh > /tmp/install_hero.sh
bash /tmp/install_hero.sh
```

## Hero for Developers

For developers, use the following commands:

```
curl 'https://raw.githubusercontent.com/freeflowuniverse/herolib/refs/heads/development/install_v.sh' > /tmp/install_v.sh
bash /tmp/install_v.sh --analyzer --herolib 
#DONT FORGET TO START A NEW SHELL (otherwise the paths will not be set)
```

## Hero with Docker

If you have issues running Hero natively, you can use the [Docker version of Hero](hero_docker.md).