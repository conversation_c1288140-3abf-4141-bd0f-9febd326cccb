---
sidebar_position: 1
title: 'What is the ThreeFold Grid?'
---

## What is the ThreeFold Grid?

The grid is a decentralized Web4 infrastructure layer which allows us to build Web4, for everyone and by everyone.

**The layer is called the ThreeFold Grid and...**

- It is capable of delivering self-healing data, network and cloud at a planetary scale.
- It allows everyone, everywhere to deploy their own autonomous Internet infrastructure. 
- It allows everyone to build the applications of the future (10x less effort, 10x more security).
- It has been operational for years as a working proof of concept with thousands of nodes online. [Check the ThreeFold Dashboard.](https://dashboard.grid.tf)


## Web4 Decentralized Architecture

The ThreeFold decentralized architecture has key features.

- It is possible for millions of Famers associated with Farmingpools (V4) to deliver the compute, AI, storage and network capacity required.
- Governance is done via the ThreeFold decentralized autonomous organization (TFDAO) with a token-based voting model.
- Web 2 and 3 solution providers & hosters can use the resources on the grid. [Check the ThreeFold Dashboard.](https://dashboard.grid.tf)
- Web 4 developers can create the apps and experiences of the future.

## Vision

The ThreeFold Grid aims to create a more autonomous, efficient, and evolutive Internet infrastructure that returns control to individual users and reduces reliance on centralized tech giants. 

> A thriving Internet is not only possible, it is being built right now.
>
> *ThreeFold is building Web4...*

Join the grid and build with us! There are many ways to contribute and interact on the grid. 

Read the [Components](/docs/category/components) section to see how you can join the Web4 phase of ThreeFold.

## The Process

- Farmers deploy nodes to the ThreeFold Grid, providing data, network and cloud resources.
- Resources are verified via the **Proof-of-Capacity** minting protocol and made available for workload deployments.
- Users can rent data, network and cloud resources directly through the network via the **Proof-of-Utilization** payment protocol.


