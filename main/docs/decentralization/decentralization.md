---
title: 'Introduction'
sidebar_position: 1
description: We aim for a platform that is as distributed and decentralized as possible.
---

<h1> Decentralization </h1>

Decentralization, in our view, isn't solely about employing blockchain technology everywhere. 

- Instead, we envision it as the result of a global community collaborating transparently, sharing everything they do, and actively seeking feedback. 
- Our goal is to ensure that the platform we deploy operates in the most distributed and decentralized manner possible.

For us decentralization is a very serious topic and is achieved in the following way:

- [DePIN](depin.md)
  - **DePIN** (Decentralized Physical Infrastructure Networks) refers to blockchain-based networks that enable decentralized ownership, operation, and coordination of physical infrastructure, such as cloud, data and network capacity, incentivized through tokenized rewards and governed by community stakeholders.
- [INCA](inca.md) 
  - The **INCA** token is the utility token as used in the ecosystem to let the DePIN flywheel work.
- [ThreeFold DAO](dao.md)
  - A **Decentralized Autonomous Organization** is an organizational structure that operates through smart contracts on a blockchain, with decision-making processes carried out in a decentralized and autonomous manner. 
- [Farming Pools](farming_pools.md)
  - A **farming pool** is a group farmers in DAO structures, which could be also legally backed by separate dedicated cooperatives.
  - Each farming pool takes care of management of SLA (service levels), pricing, support and communication to its related farmers.
  - The farming pools run their own voting process to manage their operations.
- [Cooperative](coop.md)
  - A **cooperative** acts mainly as a coordination role around development, farming pools, DAO's, upgrades, etc.
  - It manages the promotion of the TFGrid and its ecosystem.
  - It safeguards the decentralization principles and the governance of the ecosystem.
  - It requires operational tasks in relation to anything that is of interest to the ThreeFold community.
- [TF Validators](threefold_validators.md)
  - **Validators** manage the technical components which run the code for the blockchain, including the DAO, and other components needed to technically operate the TF Grid.
  - There are multiple independent validators and each of them runs a validator stack.


