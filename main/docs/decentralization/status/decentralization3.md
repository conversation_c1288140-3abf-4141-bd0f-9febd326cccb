---
title: 'ThreeFold V3'
sidebar_position: 12
description: ThreeFold V3 Decentralization Model
---

## ThreeFold V3 Decentralization Model

The following is our own subjective evaluation for the TFGrid V3 and should not be construed as a guarantee or commitment. We welcome any differing opinions or feedback.

| Description                                                | Good Enough  | Remarks                                                                                               |
| ---------------------------------------------------------- | --------------- | ----------------------------------------------------------------------------------------------------- |
| TFNodes owned and invested by independent farmers          | Yes             | Less than 5% owned by ThreeFold Cloud/Dubai                                                           |
| Open documentation & feedback                              | Yes             | Everyting is open-source on GitHub, anyone can contribute and give feedback.|
| Decentralized Autonomous Organization (DAO)                              | Yes             | ThreeFold DAO is established and there are clear processes to vote.|
| Transparancy and input on any change to do with tokenomics | Yes             | See the discussions on the ThreeFold Forum [^1] and the GEP process                                                    |
| Transparancy about tokens, history                         | Yes             | See GitHub organizations[^2][^3]                                                                                            |
| TFChain deployment                                         | No              | Too few run the validator stack, we need more validators                                              |
| Code development                                           | No              | +90% done by TF9: We need more participants                                                       |
| Funding for tech creation & TFGrid promotion               | No              | More or less all done by TF9 and TF Dubai: we need more participants to make this community-owned           |
| Input on testing cycle & collaboration                     | Yes             | We have a testnet on which everyone can contribute                                                    |
| Quality and transparancy of code                           | Yes             | Everything is on GitHub[^3]: everyone can review and comment                                                    |
| Input on process & roadmap for code development            | Yes             | Everything is on GitHub[^3]: everyone can review and comment                                                    |
| Tracking of available compute, storage, network capacity   | Yes             | Everything is tracked on the blockchain TFChain (farming)                                                        |
| Tracking of used compute, storage, network capacity        | Yes             | Everything is tracked on the blockchain TFChain (utilization)                                                    |
| Minting of tokens (farming)                                | Yes             | Code uses the information on blockchain and creates minting report                                    |
| Verification of minting of tokens (farming)                | Yes             | Minting reports checked by Guardians[^4] and hash kept on blockchain when doing the minting           |
| The actual minting                                         | Yes             | Multisignature of Guardians is needed to valudate the transactions, each minting links back to report |
| It's possible for super smart hackers to fake capacity      | No              | Probably yes on non-certified nodes, but it's not easy[^5]                                             |
| All components redundant and distributed enough        | No              | We need more TFGrid validators                                                                        |

[^1]: ThreeFold Forum [Link](https://forum.threefold.io/)
[^2]: GitHub TF Dubai [Link](https://github.com/threefoldfoundation) 
[^3]: GitHub TF9 [Link](https://github.com/threefoldtech) — List of components [Link](https://github.com/threefoldtech/home/<USER>/master/wiki/components/components_overview.md)
[^4]: There are multiple Guardians to guide this process
[^5]: They would have to re-engineer how ZOS works and tells TFChain, but human chain (i.e. Guardians) can still see. We are planning to make this 100x more difficult in V4. If a hacker succeeds, they would basically receive tokens which are not really earned. This is probably not possible on certified node, because of silicon route of trust with protected BIOS.

## Fully Decentralized Farming - TF Nodes

Today more than 500 farmers host the nodes which make up the TFGrid Network.

None of the ThreeFold companies or its team has the ability to break this model, its our aim to have the farmers independant completely from Threefold.

## Grid Enhancement Proposals (GEP)

The decisions that shape the technology and standards of ThreeFold are driven by the community through the Grid Enhancement Proposal (GEP) program. These proposals are reviewed and approved by farmers, technical steering committees as well as the cooperative, ensuring that the best ideas rise to the top. 

The GEPS are proposed by ThreeFold Dubai and published on the [ThreeFold Forum](https://forum.threefold.io) and registerd on TFChain.

The GEPS need to be voted for by the farming community.

GEPS deal with:

- Changes approvals in protocol or anything which has impact on any fundamental working of the TGGrid
- Upgrades of the TFGrid with new software
- Functionality & roadmap approvals when needed (e.g. priorities)

## TF Validator Stacks

- The TFChain is our blockchain component in V3 to provide identity, billing, capacity tracking, utilization tracking, pricing, etc. 
- TFGrid Validators are the control plane of the TFGrid and allows us to Interact with the TFGrid.
- A TFGrid Validator is combination of TFChain (blockchain node), Monitoring Nodes, TF Explorer, TF Hub, all Web Interfaces.
- TFGrid Validators are run by ThreeFold Guardians.
- The TF guardians get a monthly fixed fee for running the Validators (in TFT and/or INCA).
- ThreeFold Dubai is helping the guardians to do it right and gives support.
- Token minting (TFGrid 3) is executed by code and validated by the guardians to make sure there are no mistakes.
- Each TFGrid validator stack has a name as follows:  $location.grid.tf, e.g. the the dashboard would be `dashboard.ghent.grid.tf`, a global loadbalancer makes sure that normal users of the solution don't have to understand this naming convention.
- Code & protocol changes need to be voted for by a DAO on the TFChain, the ThreeFold blockchain.

## Main Players in the Ecosystem

- +500 Farmers, people or companies hosting the nodes of the TFGrid.
- ThreeFold Dubai (DMCC) for all the grid management, promotion and token related activities. This is our main company from which we run the open-source TFGrid project.
- ThreeFold BVI for specific token related activities.
- TF9, the tech company who develops the opensource code, all code is available for ThreeFold Dubai as well as any other opensource enthousiast, TF9 will commercialize the tech for commercial usecases. TF9 has nothing to do with the tokens TFT and/or INCA.
- ThreeFold CH (Switzerland). It is not used right now.
