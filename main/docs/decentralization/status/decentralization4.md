---
title: 'ThreeFold V4'
sidebar_position: 13
description: ThreeFold V4 Decentralization Model
---

## ThreeFold V4 Decentralization Model

The following is our own subjective evaluation for the TFGrid V4 and should not be construed as a guarantee or commitment. We welcome any differing opinions or feedback.

## Fully Decentralized Farming - TF Nodes

We expect to have many thousands of independent compute, storage, network farmers in the world. This is the true nature of our decentralization.

The communication between the marketplace, 3Nodes, farming pool agents and other participants all goes over the mycelium network which leads to a fully peer-to-peer control and communication plane. All communication is end-to-end encrypted, all messages are signed by the participants.

The billing & tracking of capacity in V4 will be based on a mutual credit and capacity accounting system which is carried and executed by the farmers in a peer-to-peer fashion.

## Farming Pools

Many people will want to be a hoster of capacity while not having to deal with the duties of being a farmer. In this case, they can connect to a farming pool and add their capacity to it.

They can chose the farming pool they like. Farming pools can be DAOs by themselves: they organize their own governance indepently of ThreeFold. A farming pool defines the price, service levels, etc. A farming pool defines how the proceeds are split over their members.

We expect that more than 80% of the capacity in the future will be provided over by farming pools. In our opinion, this leads to a highly decentralized structure while making it possible to provide strong service level agreements.

## Grid Enhancement Proposals (GEPs)

The decisions that shape the technology and standards of ThreeFold are driven by the community through the Grid Enhancement Proposal (GEP) program. These proposals are reviewed and approved farmers, technical steering committee as well as the cooperative, ensuring that the best ideas rise to the top. 

The GEPS are proposed by the ThreeFold Cooperative.

The GEPS need to be voted for by the community (voting power based on the number of INCA in the voter's account), the exact rules of voting are still being worked on.

GEPs deal with:

- Functionality & roadmap approvals (e.g. priorities)
- Approval of changes in protocol or anything which has impact on the fundamental working of the TFGrid

## Grid Upgrade Proposals (GUPs)

GUPs are much more practical. They are about providing a control path for code changes. Community members or farmers will probably lack the knowledge as required to have meaningful review capability on these proposals.

GUPs deal with:

- Code updates on the TFGrid production level

## TF Grid Guardians Run the TFGrid Validators

We have been learning from what [Hashgraph](https://hedera.com/blog/decentralized-on-hedera) did and we like their model.

ThreeFold’s validator consensus model is addressing the challenges of decentralization in a practical way. It brings together up to 9 to 30 term-limited organizations (TF Grid Guardians) from across multiple continents, ensuring a truly global perspective. 

Each TF Protector accomplishes the following:

- Operate a [TF validator clusters](../threefold_validators.md), which requires some investment and an operational team.
- Validate the code written and sign off on the changes (validation step on code & functionality).
- Validate the GEPs and execute a veto right if needed, the TF Grid Guardians can stop a GEP.
- Validate and approve the GUPs.

Each Protector member has an equal vote, which prevents any single entity from gaining undue influence and keeps the governance balanced and fair.

ThreeFold’s proof-of-stake model (V4) has been designed to prevent the concentration of power. Nodes gain influence only when $TFT or $INCA is staked to them by individual users, ensuring that the community has a direct role in the network’s governance. 

<!--
TODO: Define ## TF Cooperative
-->

## Development & Open-Source

The development of ThreeFold is an open and collaborative process. All software powering the network, including services and developer tools, is open-sourced under an Apache 2.0 license. The decentralized development model encourages contributions from a wide range of participants, including the ThreeFold developer community. This approach improves innovation and ensures that the platform continuously evolves to meet the needs of its users.