---
title: 'ThreeFold Validators'
sidebar_position: 7
description: Validators are protectors of the grid.
---

In the fourth version of ThreeFold, we will a have minimum of 9 and a maximum of 30 protectors of the ThreeFold ecosystem. These protectors will be commercial partners from different continents. Each protector will run a ThreeFold validator cluster.

## Validator Cluster Functions

Each ThreeFold validator cluster provides the following functions:

- Run the consensus chains (e.g. the TFChain for TFGrid V3, the TF Validators for TFGrid V4)
- Run the ThreeFold explorers (interfaces to allow us to digest and find the relevant information easier from the TFGrid)
- Run the ThreeFold Hub, a conversion service from docker to deduped filesystems as used for our compute workloads
- Run nodes for the bridging functionality (i.e. INCA is supported on multiple chains)
- Provide Continuous integration and continuous development (CI/CD) services for the ThreeFold Grid code
- Support all the necessary webservices (e.g. websites, manuals, user interface, etc.)
- Run the ThreeFold marketplace and the components associated with the TF Liquidity Pool