---
sidebar_position: 5
description: Farming pools help manage the grid.
---

# Farming Pools

A farming pool has certain tasks and responsibility. In exchange of their work, farming pools receive rewards. 

## Tasks and Responsibility

A Farming pool takes care of the following:

- Define service level agreements, terms and conditions, customer onboarding, etc.
- Support (e.g. to replace broken nodes)	
- Management infrastructure (e.g. monitoring, billing, etc.)	
- Sales of The Nodes
- Work with node vendors	
- Communication with the farmers of the farming pool	
- Price definitions (e.g. to define sales prices of the nodes)