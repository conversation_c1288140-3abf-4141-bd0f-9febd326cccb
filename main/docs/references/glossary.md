---
sidebar_position: 5
---

# Glossary

We provide definitions of terms you may encounter while exploring the INCA ecosystem.

## Terms and Definitions

### Compute

Compute refers to the processing power and resources required to perform calculations, execute instructions, and complete tasks. In computing, compute resources include central processing units (CPUs), graphics processing units (GPUs), and other processing units that execute software instructions.
### Network

A network is a collection of interconnected devices, such as computers, servers, and peripherals, that communicate with each other to share resources and exchange data. Networks can be wired or wireless and can range from small local area networks (LANs) to large-scale wide area networks (WANs) and the internet.
### Storage

Storage refers to the holding and preservation of data in a digital form. This can include hard disk drives, solid-state drives, flash drives, and other devices that store data. Storage can be local, such as on a personal computer, or remote, such as in a cloud-based storage service.
### Cloud

Cloud refers to a model of delivering computing services over the internet, where resources such as servers, storage, and applications are provided as a service to users on-demand. Cloud computing allows users to access and use computing resources without having to manage or maintain the underlying infrastructure.
### DePIN

A DePIN (Decentralized Physical Infrastructure Network) is a protocol that uses cryptocurrency tokens to incentivize communities to build and maintain physical infrastructure networks in a decentralized manner. These networks can range from wireless and sensor networks to energy networks and cloud storage.
### Blockchain

Blockchain is a decentralized, distributed ledger technology that enables secure, transparent, and tamper-proof transactions. It is the underlying technology behind cryptocurrencies such as Bitcoin and Ethereum, but it also has applications in supply chain management, smart contracts, and other areas.
### Operating System

An operating system (OS) is a software that manages and controls a computer's hardware and software resources. It acts as an intermediary between computer hardware and user-level applications, providing services such as process management, memory management, and input/output management. Examples of operating systems include Windows, macOS, and Linux.
### End-to-End Encryption

End-to-end encryption is a method of secure communication where only the communicating parties can read the messages. It ensures that data is encrypted at the sender's end and decrypted at the receiver's end, making it unreadable to anyone intercepting the data in transit. This provides a high level of security and privacy for online communications.