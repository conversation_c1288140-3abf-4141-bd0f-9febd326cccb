
# ThreeFold TFGrid Main Documentation - HeroScript Configuration
# Generated for comprehensive Docusaurus setup

# Basic Site Configuration
!!docusaurus.config
    name:"tfgrid-docs"
    title:"ThreeFold DePIN"
    tagline:"Internet Infrastructure for Everyone by Everyone, Everywhere"
    url:"https://docs.threefold.io"
    url_home:"docs/introduction"
    base_url:"/"
    favicon:"img/favicon.png"
    copyright:"© 2024 ThreeFold"

# SEO and Metadata Configuration
!!docusaurus.config_meta
    description:"Internet Infrastructure for Everyone by Everyone, Everywhere."
    image:"https://threefold.info/tfgrid4/img/tf_graph.png"
    title:"ThreeFold DePIN"
    keywords:"ThreeFold, DePIN, decentralized, infrastructure, blockchain, farming, cloud"

# Navbar Configuration
!!docusaurus.navbar
    title:""
    logo_alt:"ThreeFold Logo"
    logo_src:"img/logo.svg"
    logo_src_dark:"img/new_logo_tft.png"

# Navbar Items
!!docusaurus.navbar_item
    label:"ThreeFold.io"
    href:"https://threefold.io"
    position:"right"

!!docusaurus.navbar_item
    label:"Mycelium Network"
    href:"https://mycelium.threefold.io/"
    position:"right"

!!docusaurus.navbar_item
    label:"AI Box"
    href:"https://aibox.threefold.io/"
    position:"right"

# Sidebar Configuration - Main Documentation Structure
!!docusaurus.sidebar
    id:"main-sidebar"

!!docusaurus.sidebar_item
    type:"doc"
    id:"introduction"
    label:"Introduction"
    position:1

!!docusaurus.sidebar_item
    type:"category"
    label:"How It Works"
    position:2
    items:["howitworks/how_it_works", "howitworks/participants", "howitworks/compare"]

!!docusaurus.sidebar_item
    type:"category"
    label:"How to Use"
    position:3
    items:["howtouse/2_user", "howtouse/3_cloud_user", "howtouse/4_web2_developers", "howtouse/5_web3_developers"]

!!docusaurus.sidebar_item
    type:"category"
    label:"Components"
    position:4
    items:["components/3node", "components/3cloud", "components/3bot", "components/3AI", "components/3phone", "components/3router", "components/bitcoin"]

!!docusaurus.sidebar_item
    type:"category"
    label:"Become a Farmer"
    position:5
    items:["become-a-farmer/get_started", "become-a-farmer/farming", "become-a-farmer/create_a_farm", "become-a-farmer/nodes_features"]

!!docusaurus.sidebar_item
    type:"category"
    label:"Decentralization"
    position:6
    items:["decentralization/decentralization", "decentralization/depin", "decentralization/dao", "decentralization/coop", "decentralization/inca", "decentralization/farming_pools", "decentralization/threefold_validators"]

!!docusaurus.sidebar_item
    type:"category"
    label:"Tokens"
    position:7
    items:["tokens/tokenomics", "tokens/multichain"]

!!docusaurus.sidebar_item
    type:"doc"
    id:"litepaper"
    label:"Litepaper"
    position:8

!!docusaurus.sidebar_item
    type:"doc"
    id:"roadmap"
    label:"Roadmap"
    position:9

!!docusaurus.sidebar_item
    type:"doc"
    id:"tech"
    label:"Technology"
    position:10

!!docusaurus.sidebar_item
    type:"category"
    label:"References"
    position:11
    items:["references/manual", "references/glossary"]

!!docusaurus.sidebar_item
    type:"category"
    label:"Legal"
    position:12
    items:["legal/disclaimer", "legal/privacy_policy", "legal/terms_and_conditions"]

# Import External Content
!!docusaurus.import_source
    url:"https://git.ourworld.tf/ourworld_holding/docs_coordination/src/branch/main/docs/focus/threefold/geomind"
    dest:"docs"
    label:"GeoMind"
    position:5
    description:"GeoMind ThreeFold AI"

# Deployment Configuration
!!docusaurus.publish
    cat:"prod"
    dest:"<EMAIL>:/root/hero/www/info/tfgrid4"

!!docusaurus.publish
    cat:"dev"
    dest:"<EMAIL>:/root/hero/www/infodev/tfgrid4"

# Theme Customization
!!docusaurus.theme
    primary_color:"#1B4F72"
    secondary_color:"#2E86AB"
    dark_mode:true
    navbar_style:"dark"

# Footer Configuration
!!docusaurus.footer
    copyright:"© 2024 ThreeFold. All rights reserved."
    links:[
        {
            "title": "Documentation",
            "items": [
                {"label": "Getting Started", "to": "/docs/introduction"},
                {"label": "How It Works", "to": "/docs/howitworks/how_it_works"},
                {"label": "Become a Farmer", "to": "/docs/become-a-farmer/get_started"}
            ]
        },
        {
            "title": "Community",
            "items": [
                {"label": "ThreeFold.io", "href": "https://threefold.io"},
                {"label": "Forum", "href": "https://forum.threefold.io"},
                {"label": "Telegram", "href": "https://t.me/threefold"}
            ]
        },
        {
            "title": "Products",
            "items": [
                {"label": "Mycelium Network", "href": "https://mycelium.threefold.io/"},
                {"label": "AI Box", "href": "https://aibox.threefold.io/"},
                {"label": "3Node", "to": "/docs/components/3node"}
            ]
        }
    ]
