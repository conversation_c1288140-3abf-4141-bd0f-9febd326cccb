{"style": "dark", "links": [{"title": "Docs", "items": [{"label": "Introduction", "to": "/docs/introduction"}, {"label": "Litepaper", "to": "/docs/litepaper"}, {"label": "Roadmap", "to": "/docs/roadmap"}, {"label": "Manual", "href": "https://manual.grid.tf/"}]}, {"title": "Features", "items": [{"label": "Become a Farmer", "to": "/docs/category/become-a-farmer"}, {"label": "Components", "to": "/docs/category/components"}, {"label": "Tokenomics", "to": "/docs/tokens/tokenomics"}, {"label": "Technology", "to": "/docs/tech"}]}, {"title": "Web", "items": [{"label": "ThreeFold.io", "href": "https://threefold.io"}, {"label": "Dashboard", "href": "https://dashboard.grid.tf"}, {"label": "GitHub", "href": "https://github.com/threefoldtech/home"}, {"href": "https://mycelium.threefold.io/", "label": "Mycelium Network"}, {"href": "https://aibox.threefold.io/", "label": "AI Box"}]}]}