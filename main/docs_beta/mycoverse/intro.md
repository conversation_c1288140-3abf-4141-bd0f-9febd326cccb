---
title: 'Intro'
description: 'Explain the concepts of our "Universe"'
sidebar_position: 1
---

MyVerse is our implementation of Web4

- **MyVerse** = the universe in which our decentalized sovereign apps exist.
- **MySphere** = a group of people collaborating, communicating together, serves as a circle of trust.
- **MyApp** = an application as deployed in a MySphere as part of the MyVerse
- **MyGuardian** = the protectors of one MySphere.
- **MyGenie** = our personal AI assistent who deploys, monitors and manages our applications inside the MyVerse.
- **Mycelium** = our network which connects any app to any participant in the MyVerse.
- **MycoNaut** = the person being active inside the MyVerse and part of one or more MySpheres

> Above names will probably still change.

### The Concept

- A MySphere 
- All MycoNauts & MyApps communicate over the secure Mycelium Network
- All MyApps are self healing.
  - Data from a MyApp cannot be lost.
- MycoGuardian = MySphere administrators 
  - manage the MyApps
  - certain actions might require consensus between the MycoGuardians before they can be executed
  - can see all monitoring information, and as such the health of a MySphere and its MyApps

```mermaid
graph TD
  subgraph MyVerse ["MyVerse (Decentralized Universe)"]
    direction TB
    MySphere["MySphere (Circle of Trust)"]
    MycoNaut["MycoNaut (Active Participants)"]
    MyApp["MyApp (Applications)"]
    MycoGuardian["MycoGuardian (Sphere Administrators)"]
    MyGenie["MyGenie (Personal AI Assistant)"]
    FungiStor["FungiStor (Content Delivery & Storage)"]
    Mycelium["Mycelium (Secure Network)"]
  end

  %% Connections
  MySphere -->|Collaborates with| MycoNaut
  MySphere -->|Contains| MyApp
  MySphere -->|Managed by| MycoGuardian
  MyApp -->|Self-Healing| FungiStor
  MyApp -->|Communicates over| Mycelium
  MycoNaut -->|Interacts via| MyApp
  MycoGuardian -->|Monitors & Manages| MySphere
  MycoGuardian -->|Consensus Actions| MySphere
  MyGenie -->|Deploys & Manages| MyApp
  MyGenie -->|Supports| MycoNaut

```

### Financials

- BTC is the native currency of our MyVerse.
- BTC seamless maps into INCA to empower the underlying utility based transactions the resources used inside the ThreeFold Grid.
- BTC can be swapped to INCA using atomic swapps and managed by the MyGenie


### Requirements

1. **Decentralized**: No single authority controls the network, enhancing security, censorship resistance, and user autonomy.
2. **Safe**: We should not have to worry about our safety.
3. **Peer-to-Peer**: Infrastructure is distributed across nodes contributed by individuals or organizations, removing dependency on centralized data centers.
4. **Incentivized**: Participants are rewarded for contributing resources (bandwidth, storage, computing) to the network, fostering growth and sustainability.
5. **Interoperable**: Open protocols and standards ensure that different networks and systems can work seamlessly together.

