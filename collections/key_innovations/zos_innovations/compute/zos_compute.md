---
title: 'Zero-OS - Compute System'
sidebar_position: 1
description: 'Technical architecture of the Zero-OS distributed compute system'
hide_title: true
---

![](img/zos_intro.png)

# Zero-OS Compute System

Zero-OS is a minimalist operating system built on the Linux kernel, designed specifically for distributed computing environments. It eliminates unnecessary complexity while focusing on essential system functions.

## Core Architectural Principles

Zero-OS is designed around three fundamental principles:

1. **Autonomy**: The system operates without requiring remote or local administrative maintenance, which is essential for a globally distributed grid architecture.

2. **Simplicity**: The OS implements only essential components, reducing complexity and enabling deployment with minimal resource requirements.

3. **Stateless Design**: Zero-OS instances maintain no persistent local state, with state information stored within the grid itself, ensuring system stability despite node failures.

## System Architecture

### Core Components

Zero-OS implements a minimalist architecture supporting only essential primitives:

1. **Compute Capacity Management**: Allocates computing resources through lightweight virtualization
2. **Storage Capacity Management**: Implements file deduplication and distributed storage mechanisms
3. **Network Capacity Orchestration**: Provides secure overlay networking with cryptographic verification

### Compatibility Layer

- Docker container support
- Virtual Machine (VM) compatibility
- Linux workload support
- Integrated distributed storage and network primitives
- Smart contract integration for IT resource allocation

## Technical Implementation

The distributed architecture generates compute capacity through several key components:

- **ZKube**: Specialized Kubernetes deployment optimized for distributed environments
- **Zero VM**: Container/VM runtime environment providing isolated execution environments
- **CoreX**: Optional process management system for secure remote access to Zero VM instances

## Security Architecture

Zero-OS incorporates security at every level:

- **File Integrity Verification**: Cryptographic fingerprinting verifies application integrity before launch
- **Reduced Attack Surface**:
  - No shell or server interface exposed in the operating system
  - End-to-end encrypted network communication between nodes
  - Network/compute isolation architecture separating compute/storage from network services
- **Container Isolation**: Enhanced security through dedicated virtual machines for container execution
- **Autonomous Operation**: Reduction of attack vectors by eliminating human error potential

## Deployment Process

Zero-OS implements a network boot architecture that delivers the OS to nodes over the internet:

1. Hardware deployment at the desired location
2. Resource farm configuration via grid explorer interface
3. Bootloader configuration via USB media or network boot
4. Network initialization and power-on
5. Automatic OS component retrieval from the network

This enables a stateless architecture where the OS runs without installation on local storage, enhancing security and deployment efficiency.

## Current Implementation Status

The core Zero-OS compute technology has been in production for multiple years:

- **Zero-OS Core**: Production implementation
- **Zero-Images Architecture**: Production implementation
- **Zero-Install Architecture**: Production implementation
- **Smart Contract for IT**: Available for OEM (general availability in ZOS v4 release, H2 2025)
- **Deterministic Deployment**: Available for OEM (general availability in ZOS v4 release, H2 2025)
