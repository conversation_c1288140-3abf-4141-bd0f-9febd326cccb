---
title: Geo-Aware Cloud Architecture
sidebar_position: 3
---


## Distributed IT Capacity Architecture

Zero-OS implements a distributed architecture for generating compute, storage, and network capacity resources.

### Compute Components

- **ZKube**
    - Kubernetes deployment architecture
- **Zero VM**
    - Container or virtual machine runtime environment within Zero-OS
- **CoreX**
    - Process management system (optional implementation) providing remote access to Zero VM instances

A node in this architecture consists of Zero-OS deployed on compatible hardware hosted by various infrastructure providers.

### Storage Subsystems

The architecture implements four distinct storage mechanisms for data management:

- **Zero-OS Filesystem**
    - Deduplication-enabled filesystem architecture replacing traditional container images
- **Zero-OS Mount**
    - SSD-based mounted storage location optimized for high-performance I/O operations
- **Quantum Safe Filesystem**
    - Distributed storage architecture implementing data integrity protection against loss or corruption, designed for secondary storage applications
- **Zero-OS Disk**
    - Virtual disk technology implementation (available to OEM integration partners)

### Network Connectivity Architecture

The system implements three network connectivity models for virtual machine instances:

- **Mycelium Network**
    - Globally distributed overlay network architecture
    - Client implementations available for Windows, macOS, Android, and iOS platforms
- **ZOS Router**
    - Direct connection to public IP address space
- **Web Gateway**
    - Secure proxy architecture enabling controlled internet traffic routing to isolated virtual machine instances
