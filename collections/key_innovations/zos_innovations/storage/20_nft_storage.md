---
sidebar_position: 20
title: NFT Storage
hide_title: true
---

## Distributed Storage System for NFT (H2 2025)

Planned for H2 2025 and is part of the [Fungistor System](19_fungistor_innovation.md).

## Technical Specifications


NFTs (Non-Fungible Tokens) can be stored using FungiStor + QSFS + ZSTOR architecture, 

ensuring:

- NFT Metadata (JSON, image, etc.) is stored in QSFS, distributed over ZSTOR and ZDBs
- The NFT token itself (on-chain) contains a pointer (e.g. QSFS link, hash, or content ID)
- Retrieval goes through Decentralized DNS → HTTPS Frontend → QSFS

advantages:

- Streamlined upload process for NFT owners
- Global retrieval capability with integrated data verification
- Multi-interface availability (IPFS, HTTP(S), etc.) for global access
- Built on a distributed storage architecture optimized for energy efficiency, security, and privacy
- Data ownership remains with the NFT owner

## Technical Architecture

![Diagram](https://kroki.io/plantuml/svg/eNqVVE1T2zAQvetXbNMDMCXJlckwDB9pWi5AcZjOcOkIe22rsbWuJCcTOvz37sqBOHwceoojvX27eu9Jn0KJNcJD1WLh9Fqd-qBdaOtK-YWxjXa6hgedLgpHrc0uqCIHwWnreQtt6KFSqhuyvJaEdYXgMA3aFhX2IL7UGa2MLSDXle_vnDlHq47989HRkXqPF_4qgHNyGboekpdejbcqTUD11KN4meX_KABmZEMP-KRUoAYCwQOFQDVkRqgNWVWaDAHrJqwhQ58608RltQdfbXBraMjYMIE7j8yubfDCsjS4Am3hajZX22MOImgMP3VVYeCP6VnTDED7WC2U5xWli7TUxvbLmAWSmt2DCx7b6TTAPtlhxB3EeunD5dOrhCXxVLUyI3yBbzrgis3vkU0xRSGpzCNmUhEJ-LcP-j6f3yQwc9wObQb7G56uWdyUdj-SWcJN7pP59S1wvNKF2hoySAI5XSBcUYaD6M-2gVTuLESOnZXB_fQcboiq2JP__JI_4tReFyo_gbyiFVDeP7PmcdM4doAcQ1qqKPpweCIiwQSOU7F9IrafcOGfFn2I-sm2wCL-DS60zvruxHe3l8DBKbUv1ZZdtN-tilMtMfYtNkYIStCdwLv4xtFSwmZZMDA2py35e_B4OhjbPIyPTXYy-u0llh1SauKsryfqjpsbvr1xX4Cdf7vIimjRNmwun9wZXKLqUBG_8eINuc4gZ8tr1t4rS4HfClOUQRwSgQGubbWO7nBwfXdz-ID7OCpGcdzJeHxxOX0W90BJ9oRHvWLrEghwlvJ143Rs1IUE3RL91n_HNzlG7SOiTQ7FoRqDznTQh2Bqju0hiBd0CKzyiMNNjm9LiQ4_5HqJKL8uzyrwO-JZv4c2cLVE8xEdDReWVhVmfDc63h7lKX_KG_0P52zkEQ==)

<!--
!theme bluegray
@startuml
skinparam backgroundColor transparent
skinparam componentStyle rectangle
skinparam shadowing false
skinparam ArrowColor #888

skinparam component {
  BorderColor #888
  BackgroundColor white
}
skinparam rectangle {
  BorderColor #888
  BackgroundColor white
  FontColor #888
}

top to bottom direction
hide empty description

' Entry point: User wants to view an NFT
component "User / Wallet / DApp" as User

' Blockchain
component "NFT Smart Contract (on-chain)" as NFT

' DNS resolution + Gateway
component "Decentralized DNS" as DNS
component "HTTPS Frontend (Gateway)" as HTTPS

' QSFS + ZSTOR stack
rectangle "Storage Node" {
  component QSFS
  component ZSTOR
  component "ZDB Pool" as ZDB_Pool
}

' Arrows: flow of resolution and content fetch
User \-\-\> NFT : <color:#888> request NFT
NFT \-\-\> User : <color:#888> returns QSFS URI or hash

User \-\-\> DNS : <color:#888>resolve NFT gateway
DNS \-\-\> HTTPS : <color:#888>provide node info
User \-\-\> HTTPS : <color:#888>fetch /nft/<id>.json

HTTPS \-\-\> QSFS : <color:#888>request file
QSFS \-\-\> ZSTOR : <color:#888>lookup + retrieve
ZSTOR \-\-\> ZDB_Pool : <color:#888>read fragments
note right of NFT
  Only contains pointer (e.g. QSFS://CID or hash)
end note

note right of HTTPS
  Acts as gateway Serves content from QSFS
end note

note right of QSFS
  NFT metadata, image, video, etc. stored here
end note

note right of ZDB_Pool
  Fragments distributed and zero-knowledge stored
end note

@enduml

-->

<!-- [Edit this diagram](https://niolesk.top/#https://kroki.io/plantuml/svg/eNqVVE1T2zAQvetXbNMDMCXJlckwDB9pWi5AcZjOcOkIe22rsbWuJCcTOvz37sqBOHwceoojvX27eu9Jn0KJNcJD1WLh9Fqd-qBdaOtK-YWxjXa6hgedLgpHrc0uqCIHwWnreQtt6KFSqhuyvJaEdYXgMA3aFhX2IL7UGa2MLSDXle_vnDlHq47989HRkXqPF_4qgHNyGboekpdejbcqTUD11KN4meX_KABmZEMP-KRUoAYCwQOFQDVkRqgNWVWaDAHrJqwhQ58608RltQdfbXBraMjYMIE7j8yubfDCsjS4Am3hajZX22MOImgMP3VVYeCP6VnTDED7WC2U5xWli7TUxvbLmAWSmt2DCx7b6TTAPtlhxB3EeunD5dOrhCXxVLUyI3yBbzrgis3vkU0xRSGpzCNmUhEJ-LcP-j6f3yQwc9wObQb7G56uWdyUdj-SWcJN7pP59S1wvNKF2hoySAI5XSBcUYaD6M-2gVTuLESOnZXB_fQcboiq2JP__JI_4tReFyo_gbyiFVDeP7PmcdM4doAcQ1qqKPpweCIiwQSOU7F9IrafcOGfFn2I-sm2wCL-DS60zvruxHe3l8DBKbUv1ZZdtN-tilMtMfYtNkYIStCdwLv4xtFSwmZZMDA2py35e_B4OhjbPIyPTXYy-u0llh1SauKsryfqjpsbvr1xX4Cdf7vIimjRNmwun9wZXKLqUBG_8eINuc4gZ8tr1t4rS4HfClOUQRwSgQGubbWO7nBwfXdz-ID7OCpGcdzJeHxxOX0W90BJ9oRHvWLrEghwlvJ143Rs1IUE3RL91n_HNzlG7SOiTQ7FoRqDznTQh2Bqju0hiBd0CKzyiMNNjm9LiQ4_5HqJKL8uzyrwO-JZv4c2cLVE8xEdDReWVhVmfDc63h7lKX_KG_0P52zkEQ==) -->


### User-Controlled Data Persistence

The architecture implements a user-controlled approach rather than a shared-all model.

Data storage administrators maintain control over:

- Geographic data placement parameters
- Redundancy configuration
- Data retention policies
- Content delivery network configuration (availability zones and duration)

### Technical Reliability Mechanisms

- Data integrity verification through cryptographic hashing
- Redundancy mechanisms to prevent data loss
- Automatic integrity verification through hash (fingerprint) validation with self-healing capabilities
- Per-owner encryption and compression implementation
- Configurable redundancy levels

### Lookup Architecture

- Integration with Mycelium Network protocols
- Multi-protocol URL and storage network support
- Compatible with IPFS and HyperDrive URL schemas
- Implements unique DNS schema with globally unique identifiers

### Content Delivery Implementation

Files are distributed across multiple geographic locations for optimized access.

Each file is assigned a unique URL for retrieval from any available storage location.

Edge caching is implemented at each endpoint for improved performance.

### Self-Healing Storage Architecture

The system implements automatic detection and correction of data corruption (e.g., bitrot).

In hardware failure scenarios (storage device or node), the system automatically redistributes data according to the configured redundancy policy.

### Underlying Storage Algorithm

- Implements a distributed storage system using quantum-safe security protocols
- Designed for global-scale operation with significant efficiency advantages over traditional replicated systems
- Utilizes forward-looking error correcting codes for data integrity

### Energy Efficiency

The storage architecture requires approximately 3-10x less energy compared to traditional replicated systems.

### Multi-Protocol Interface Implementation

The stored data is simultaneously accessible through multiple protocols:

- Interfaces:
  - IPFS
  - HTTP(S)
  - Filesystem

This multi-protocol approach provides flexibility for various integration scenarios.

Content objects (videos, images, etc.) can be embedded in any HTTP-compatible platform.
