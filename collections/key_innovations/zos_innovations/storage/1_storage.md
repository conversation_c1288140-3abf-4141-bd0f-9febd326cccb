---
sidebar_position: 1
title: 'Zero-OS Storage System'
description: 'Technical architecture of the Zero-OS distributed storage system'
---

# Zero-OS Storage System

The Zero-OS storage system implements a distributed architecture designed for data integrity, security, and scalability across decentralized environments.

## Core Architectural Principles

1. **Dispersed Storage**: Data is mathematically encoded and distributed across multiple nodes, with each node storing only small, incomplete fragments.

2. **Zero Knowledge Design**: Storage nodes have no knowledge of what they're storing, as each contains only mathematical fragments insufficient to reconstruct the original data.

3. **Self-healing Architecture**: The system automatically detects and corrects data degradation (bitrot) and redistributes data in case of hardware failures.

4. **Geographic Control**: The architecture enables precise selection of storage providers based on specific requirements, including regulatory compliance needs.

## System Architecture

The Zero-OS storage system employs a layered architecture with three distinct component levels:

### Frontend Interface Layer
- **QSFS** (Quantum Safe File System): Provides a standard filesystem interface, making the underlying distributed storage accessible to applications

### Encoding & Distribution Layer
- **ZSTOR**: Handles data encoding, encryption, metadata management, and distribution of fragments
- Implements the zero-knowledge quantum safe encoding algorithm
- Contains monitoring capabilities for system health checks

### Physical Storage Layer
- **ZDB** (Zero-DB): The fundamental storage daemon that stores data fragments directly on HDD or SSD
- Implements an append-only database architecture to store objects in immutable format
- Optimized for high-performance writes to storage media

## Scale-Out Architecture

This architecture is based on a scale-out model:

- Each storage use case operates with its own instance of QSFS and ZSTOR
- These instances connect to 20+ ZDBs, which can be geographically distributed
- The architecture can support unlimited numbers of QSFS and ZSTOR instances
- Data is always processed and consumed locally where it is produced

![Diagram](https://kroki.io/plantuml/svg/eNqVlDFvwjAUhHf_ilc6tB0YMAxMFSSIsahNB_BSmcQkFokdOUYIVf3vdUJCDLaKukWfz3f3bCsPOmMFg21-YKmiJzSrNFX6UOSo2nNRUkUL2NJ4nyp5EEkoc6lAKyoqs8SEtlSxLEopDIv0KWegWKypSHNmSaqMJvLIRQo7mlf2ylwpeTy7P06nU-TzhW8EEEiVMGUpDbqpd8y4ZujH9riU-Z8HwFIKbQmNac52GrQExdNMQ8Jray4FynjCgBWlPkHCqljxssGojx7UIUwkQFYRvEkjnw-aPv2EZBF8zUcuwi4a12X-Mg985oFrHuB7TqHPKXSdQrdmOHbR5CZvqcwhXwWuncDoc_Xxtb6OfI-W0S1rhdgjxHdjN_7YjSfWsHtuxO9GPG7ENwRpCj81ivrBNRS1Q8Nw-NqfSjugDTFqe1pw0yqJDU22CWm-m5RFAM8FqzKWvKAu4Cw_v02HBReGex12mXkKqOvRb8YuC0cehrvNxGoz7piVMnGZCUEzczf1X-0XeNeKkQ==)

## Technical Implementation

### Quantum Safe Storage (QSS) Algorithm

Unlike conventional storage systems that use replication or sharding, the QSS algorithm implements a mathematical transformation approach:

1. Original data is fragmented into multiple parts
2. The system generates unique mathematical equations using these fragments
3. These equations are distributed across multiple physical storage devices
4. The original data fragments are not retained; only the equations are stored
5. To recover data, the system retrieves multiple equations and solves them

In a typical production configuration (16/4):
- Each object is fragmented into 16 parts
- 20 equations are generated and distributed across 20 devices
- Only 16 equations are needed to reconstruct data
- The system can tolerate the loss of any 4 of the 20 equations

This mathematical approach achieves an overhead of only 20% compared to the 400% overhead typical in traditional replication systems.

### Component Details

#### ZDB (Zero-DB)
- Redis-compatible storage daemon optimized for high-performance writes
- Implements an append-only database storing objects in immutable format
- Supports namespaces uniquely reserved per user or system
- Stores only data fragments without any context

#### ZSTOR
- **ZSTOR Encoder**: Performs data compression, encryption, and applies forward error correction
- **ZSTOR ZDB**: Local staging area for data aggregation and performance
- **ZSTOR Monitor**: Exposes monitoring metrics and health/status information
- **ZSTOR Meta**: Stores metadata within its own ZSTOR ZDB
- **ZSTOR Cluster**: (OEM only) Enables real-time synchronization between multiple ZSTOR instances

#### QSFS (Quantum Safe File System)
- Provides filesystem abstraction layer 
- Supports petabyte-scale storage capacity
- Data transfer performance of up to 50 MB/sec
- Support for up to 2 million files per filesystem instance
- Universal mounting capability across operating systems and container platforms

## Implementation Status

- **ZDB (Zero-DB)**: Production implementation
- **ZSTOR (Zero-Stor)**: Production implementation
- **QSFS (Quantum Safe File System)**: Production implementation
- **FungiStor**: Planned for H2 2025
- **NFT Storage**: Planned for H2 2025

## Current Limitations

While the current implementation is technically robust, there are some limitations:

1. **Technical Expertise Requirement**: The system currently requires scripting knowledge and technical expertise
2. **Limited User Interface**: The system lacks user-friendly interfaces for non-technical users
3. **Complex Configuration**: Setting up and optimizing the system requires understanding the lower-level components

The upcoming FungiStor implementation aims to address these limitations by providing a more user-friendly abstraction layer that simplifies the entire experience, with automated coordination, smarter defaults, and integrated content addressing.
