---
title: 'Quantum Safe File System'
sidebar_position: 2
description: 'Technical implementation of the QSFS distributed filesystem'
---

# Quantum Safe File System (QSFS)

QSFS provides a filesystem abstraction layer that makes the underlying Zero-Stor technology accessible to applications, addressing the gap between the highly secure but technically complex Zero-Stor implementation and the practical needs of developers and system administrators.

## Technical Architecture

QSFS implements a filesystem architecture that utilizes Zero-Stor as its backend storage mechanism:

1. Applications interact with QSFS as a standard mounted filesystem
2. QSFS translates filesystem operations into Zero-Stor operations
3. Zero-Stor handles the encoding, encryption, and distribution of data
4. Data retrieval reverses this process, reconstructing files from distributed fragments

![](https://kroki.io/plantuml/svg/eNqVlD9v2zAQxXd-imsydKgzxH8Ao0OROI2nokGrTl4CijxJhCVSIE8whCLfvUcFThSJHjrq3t1P9x4pfaIKG4S87rD0shd3gaSnrqlFOBrbSi8byKU6lt51Vj-42nkgL21gCS2NupRrWme5llFfI3hUJG1Z46glVFK7k7ElFLIOY-Xee3d6pV9vt9sUFv4KgJ3zGv2okUuT7U6VIRQvI8TbKv-HANg7S6PGFyHItUAOckfkGtAmoo2zojIaAZuWetAYlDftUBafmaG6gBp-ZfsMOFx1FO_7XO09vwKthsNTBj-dxqthx3fTcexD4ZD9efr9_GgVN_uEcvi-e_7hlKzjusNLb26-TaY-PI30t1n4CnlXFCx-AVmWHkvJgQjriA_WlBWBK86r7U2NoQ-EDRj24gupEArOTLZtiGEFch5BS5IiOo2QKWpq6oFNeQxhAWiV71ta4KAtBrtRGaE45HiA5xQt9wU4GaqA_YRx2ueQ4XYac3R-ez8v7WKKKcIyRVjOCcuLhFWKsJoTVhcJ6xRhPSesLxI2KcJmTtgMBA46M01X812A4Tzil6xNIG_ybrjviYv1Gmxa4HDSwurSxPrSBC8txB3fgfjr-gdSCZZG)

The filesystem maintains metadata redundancy with configurable consistency levels, typically operating with a maximum synchronization lag of approximately 15 minutes.

## Component Architecture

The QSFS implementation consists of three primary components:

1. **0-db-fs**: Implements the filesystem driver interface:
   - Uses 0-DB as its primary storage engine
   - Manages directory and metadata storage in a dedicated namespace
   - Stores file payloads in a separate namespace
   - Provides a mountable filesystem interface for applications

2. **0-stor-v2**: Handles data dispersion by implementing 'forward-looking error-correcting code' (FLECC):
   - Processes files in any format
   - Applies AES encryption using user-defined keys
   - Performs FLECC encoding
   - Distributes fragments across multiple 0-DB instances
   - Maintains data accessibility even when some 0-DB instances are unavailable
   - Rebuilds missing fragments to restore full consistency

3. **0-db**: The core storage engine:
   - Implements an append-only database that stores objects in immutable format
   - Enables built-in history preservation
   - Provides optimized disk performance
   - Minimizes overhead through simplified data structures
   - Supports efficient backup operations through linear copying of immutable files

## Technical Specifications

- **Storage Capacity**: Petabyte-scale with defined operational parameters
- **Performance**: Data transfer up to 50 MB/sec, optimized for larger file operations
- **File Count**: Support for up to 2 million files per filesystem instance
- **Compatibility**: Universal mounting across operating systems and container platforms
- **Workload Support**: Compatible with most data storage requirements (excluding high-performance database workloads)

## Security Implementation

QSFS inherits Zero-Stor's security architecture:

- Data fragmentation prevents any single storage provider from accessing complete data
- Post-quantum cryptographic protection through mathematical dispersion
- End-to-end encryption with user-controlled keys
- No single point of compromise in the architecture

## Data Integrity Protection

The filesystem implements several mechanisms to ensure data integrity:

- Automatic detection and repair of data corruption (bitrot)
- Self-healing architecture that restores redundancy levels after node or disk failures
- Persistent storage with no automatic deletion mechanisms
- Regular consistency checks across distributed fragments

## Deployment Architecture

QSFS can be deployed in various configurations:

1. **Direct Mounting**: The filesystem can be mounted directly by applications for native file access

2. **Storage Service Backend**: QSFS can serve as the backend for:
   - S3-compatible storage servers (e.g., Minio)
   - Web-based file servers (e.g., OwnCloud, NextCloud)
   - Backup systems
   - FTP servers
   - IPFS and Hypercore protocol integrations

3. **Container Integration**: The filesystem can be mounted within:
   - Docker containers
   - Kubernetes persistent volumes
   - Virtual machines

The architecture supports both on-premises deployment and distributed configurations across multiple geographic locations, providing regulatory compliance support through encryption and data fragmentation that assist with requirements like GDPR.

## Current Limitations

While the current implementation is technically robust, it has some limitations:

1. **Technical Expertise Requirement**: Deployment and configuration requires understanding of the underlying Zero-Stor architecture
2. **Management Complexity**: Ongoing management and monitoring requires technical skills
3. **Configuration Overhead**: Optimal configuration for specific workloads requires technical expertise

These limitations will be addressed in the upcoming V4 release, which aims to simplify deployment, management, and configuration while maintaining the technical advantages of the current architecture.
