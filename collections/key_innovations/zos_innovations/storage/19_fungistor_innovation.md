---
title: <PERSON>giS<PERSON>
sidebar_position: 19
---

## FungiStor – Distributed Storage Fabric for Content Delivery (H2 2025)

### Technical Challenge

Current decentralized content delivery and storage systems face significant limitations in both cost efficiency and technical capabilities.

The [QSFS](2_qsfs_innovation.md) system provides the foundation for addressing many of these limitations through an optimized architectural approach.

Content delivery represents a major infrastructure cost for media-rich platforms. A social video platform supporting 10 million users can incur infrastructure costs in the millions of USD per month.

### Key Characteristics:


FungiStor implements a peer-to-peer (P2P) content delivery architecture designed for efficient storage and distribution of diverse digital objects, including images, videos, and files.

The system architecture is designed to scale to billions of objects with optimized resource utilization. This makes it particularly suitable for content delivery network (CDN) implementations, reducing infrastructure costs for organizations that deliver substantial data volumes.

This technology will support the Sikana education platform's content distribution requirements.


- **Multi-Filesystem Support**: Multiple independent QSFS instances (each with their own ZSTOR) map to a **shared pool of ZDBs** across backend ZOS nodes.
- **Shared Storage Backend**: All QSFS/ZSTOR pairs use a **common distributed pool** of ZDBs, optimizing storage utilization and redundancy.
- **Controlled Write Access**: Write operations are **strictly serialized** via **RAFT consensus** — ensuring **only one ZSTOR** can write at any given time, preventing conflicts or corruption.
- **Dispersed Encoding Strategy**: Data encoding is highly distributed across the ZDB pool to maximize redundancy, availability, and content delivery performance.
- **Read-Optimized Architecture**: Ideal for use cases where **many frontends** need to fetch content in parallel from **diverse nodes** with low latency.

### FungiStor Technical Architecture


![Diagram](https://kroki.io/plantuml/svg/eNqVVE1v2zAMvetXcNmhGJCi16CHoR9Z0B3adXNOuwyKRdtC9eFRdANv6H8fZaeJk62HnSyTT4-Pj7TfcYMeYeM6rEn36iqxJu68U-nJhlaT9rDR5VNNsQvmNrpIwKRDkhQGnqDK6NsYJFZw7xAIS9ahdjiBpEabuLWhhkq7NM1cE8XtyP5-sViof_HCbwVwE8kgTZASOpG3bSyjeplQ7LX8HwXAKgaeAF-U4tgCR9hE5ujB2ExtY1CNNQjoW-7BYCrJtkNYncGnwNRDG624dehldiMNJyS4gFtnJTIDnXbHfGuJpZxIO_sLDSwfCnC6R5oy_AUZKOSZ79-t148FhGhQHbqfjdEVSVsYDDxIejZ4cqAdID8KpGepNk18LVbFUeB7sf7yLXtyNugjTNE9Y5qWHvuB8_OPA-TyFQQmem2DysGc3OEu4fOjGJJlwbUxAk5Tiqk0wY51CH92mFgdJTM66xVUhVw2ojp3zGoI5uwgfhCkzcWWZNxQka69gJJSIco72bphiNVgKZxMJGif9zj1idFn0H1forOd_6Cys5nglObE2TsdjBO7bBBLM9dRP2kOAzKBfJBsS5AdNH2uWu6beavQblJLm5jspmORW1kpNWqVPUSqdIlv3h8HC3Cvg65FgUfWRrOeA4pWI1rne7PkE9hVyet-YLySY_6L_AFGS32A)

<!--
!theme bluegray
@startuml
skinparam backgroundColor transparent
skinparam componentStyle rectangle
skinparam shadowing false
skinparam ArrowColor #888

skinparam component {
  BorderColor #888
  BackgroundColor white
}
skinparam rectangle {
  BorderColor #888
  BackgroundColor white
  FontColor #888
}

top to bottom direction
hide empty description

' Entry point
component "Browser / Client" as Client

' Decentralized DNS layer
component "Decentralized DNS" as DNS

' HTTPS node
rectangle "HTTPS Frontend Node" {
  component HTTPS_Server
  component QSFS
  component ZSTOR
}

' DNS resolves HTTPS node
Client \-\-\> DNS : resolve domain
DNS \-\-\> Client : IP / Node Address
Client \-\-\> HTTPS_Server : HTTPS request
HTTPS_Server \-\-\> QSFS : fetch content
QSFS \-\-\> ZSTOR : read/write fragments

note right of DNS
  Decentralized naming system of Mycelium)
end note

note right of HTTPS_Server
  Handles incoming HTTPS requests, Serves static or dynamic content
end note

note right of QSFS
  Distributed filesystem interface
end note

note right of ZSTOR
  Manages metadata, encoding, fragment distribution
end note

@enduml

-->
<!-- 
[Edit this diagram](https://niolesk.top/#https://kroki.io/plantuml/svg/eNqVVE1v2zAMvetXcNmhGJCi16CHoR9Z0B3adXNOuwyKRdtC9eFRdANv6H8fZaeJk62HnSyTT4-Pj7TfcYMeYeM6rEn36iqxJu68U-nJhlaT9rDR5VNNsQvmNrpIwKRDkhQGnqDK6NsYJFZw7xAIS9ahdjiBpEabuLWhhkq7NM1cE8XtyP5-sViof_HCbwVwE8kgTZASOpG3bSyjeplQ7LX8HwXAKgaeAF-U4tgCR9hE5ujB2ExtY1CNNQjoW-7BYCrJtkNYncGnwNRDG624dehldiMNJyS4gFtnJTIDnXbHfGuJpZxIO_sLDSwfCnC6R5oy_AUZKOSZ79-t148FhGhQHbqfjdEVSVsYDDxIejZ4cqAdID8KpGepNk18LVbFUeB7sf7yLXtyNugjTNE9Y5qWHvuB8_OPA-TyFQQmem2DysGc3OEu4fOjGJJlwbUxAk5Tiqk0wY51CH92mFgdJTM66xVUhVw2ojp3zGoI5uwgfhCkzcWWZNxQka69gJJSIco72bphiNVgKZxMJGif9zj1idFn0H1forOd_6Cys5nglObE2TsdjBO7bBBLM9dRP2kOAzKBfJBsS5AdNH2uWu6beavQblJLm5jspmORW1kpNWqVPUSqdIlv3h8HC3Cvg65FgUfWRrOeA4pWI1rne7PkE9hVyet-YLySY_6L_AFGS32A) -->

> The access points can run in ZOS but also on each Mycelium Network Node (on mobile, desktops, ...), this leads to an incredibly interesting distribution mechanism of content.



![Diagram](https://kroki.io/plantuml/svg/eNqVlc9uGjEQxu9-iml6SHLg0PyROASU9abcCC1EQkWVkNk1ywqvZ2WbIFpF6jNU6qWXvmIfoeMlhGVNq_SGvhl_v1n7s3njFrKQMFMrmRmxYbfWCeNWhWJ2metSGFHATCTLzOBKpzEqNOCM0JZKUrtaV4JFiZq0kdsoCUYmTuhMyVqLXYgU17nOYC6UrVciY3C9dX_bbreP2cJXBsDRpNLUGklqTLde5E6yp5rFyyj_ZwHQQ-1qjU-MOSzBIczQOSwgzb11jpot8lSCLEq3gVTaxORlJbNT6HQ60DNkJHUK90hVL7H9TCcHVSBYf5NIla9oV06qifdb8HHUG02jA2kyehgMpxHc3AzH3S7NuO2BVqu7q7HX0fhRGj9C45422NN4jcZfSYuP0uIjtLhBi2u0eLfFowXlMa1O0uM-IKrmPj-3TO74tnx2l1tn8tnKkYqP0sBkMALtj-i8ORwtmr4LlItAuQyUq0C5PlD60f2naX8wfD-lmgXwKTulz8E1CKW2n2lBJIm01t8cuq9gBV1Z3852p1_tSDVjU7kIlMtAuQqU6wPlcEbGdiloUnlA5QGVB1QeUPk_qHFAjQNqHFDjgBoH1PgoVaOTu_uO85crBfD7189vENH1f5QwjHoPoKSghwXGhh6PltRipihXZ8PxOfOJ9D5_ceOV24_v9N4ohWvyGJJVC7Xa0PpBfX1g8JynaB8V62O-jYjUidmUFO_Pem5EVvi8lVX0q6z5vi_SYGupcU3DZrKOuqWf_n_gD24e79Y=)

<!--
!theme bluegray
@startuml
skinparam backgroundColor transparent
skinparam componentStyle rectangle
skinparam shadowing false
skinparam ArrowColor #888
skinparam component {
  BorderColor #888
  BackgroundColor white
}
skinparam rectangle {
  BorderColor #888
  BackgroundColor white
  FontColor #888
}

top to bottom direction
hide empty description

' === Frontend Nodes ===
rectangle "Frontend Node or Mycelium A" {
  component QSFS_A
  component ZSTOR_A <<RW>>
}
QSFS_A \-\-\> ZSTOR_A

rectangle "Frontend Node or Mycelium B" {
  component QSFS_B
  component ZSTOR_B <<RO>>
}
QSFS_B \-\-\> ZSTOR_B

rectangle "Frontend Node or Mycelium C" {
  component QSFS_C
  component ZSTOR_C <<RO>>
}
QSFS_C \-\-\> ZSTOR_C

' === Shared Backend Pool ===
rectangle "Shared ZDB Pool (Distributed over ZOS nodes)" {
  component ZDB_1
  component ZDB_2
  component ZDB_3
  component ZDB_4
  component ZDB_5
  component MANY_MORE_ZDBs  
}

' Show all ZSTORs accessing the same ZDBs
ZSTOR_A \-\-\> ZDB_1
ZSTOR_A \-\-\> ZDB_2
ZSTOR_A \-\-\> ZDB_3
ZSTOR_A \-\-\> ZDB_4
ZSTOR_A \-\-\> ZDB_5
ZSTOR_A \-\-\> MANY_MORE_ZDBs

ZSTOR_B \-\-\> ZDB_1
ZSTOR_B \-\-\> ZDB_2
ZSTOR_B \-\-\> ZDB_3
ZSTOR_B \-\-\> ZDB_4
ZSTOR_B \-\-\> ZDB_5
ZSTOR_B \-\-\> MANY_MORE_ZDBs

ZSTOR_C \-\-\> ZDB_1
ZSTOR_C \-\-\> ZDB_2
ZSTOR_C \-\-\> ZDB_3
ZSTOR_C \-\-\> ZDB_4
ZSTOR_C \-\-\> ZDB_5
ZSTOR_C \-\-\> MANY_MORE_ZDBs

note bottom of ZSTOR_A
  🚀 Active RAFT leader Write-enabled (RW)
end note

note bottom of ZSTOR_B
  🕒 Follower Read-only (RO)
end note


note bottom of ZDB_5
  All ZSTORs share same encrypted\nfragment pool (ZDBs are zero-knowledge)
end note

@enduml

-->

<!-- [Edit this diagram](https://niolesk.top/#https://kroki.io/plantuml/svg/eNqVlc9uGjEQxu9-iml6SHLg0PyROASU9abcCC1EQkWVkNk1ywqvZ2WbIFpF6jNU6qWXvmIfoeMlhGVNq_SGvhl_v1n7s3njFrKQMFMrmRmxYbfWCeNWhWJ2metSGFHATCTLzOBKpzEqNOCM0JZKUrtaV4JFiZq0kdsoCUYmTuhMyVqLXYgU17nOYC6UrVciY3C9dX_bbreP2cJXBsDRpNLUGklqTLde5E6yp5rFyyj_ZwHQQ-1qjU-MOSzBIczQOSwgzb11jpot8lSCLEq3gVTaxORlJbNT6HQ60DNkJHUK90hVL7H9TCcHVSBYf5NIla9oV06qifdb8HHUG02jA2kyehgMpxHc3AzH3S7NuO2BVqu7q7HX0fhRGj9C45422NN4jcZfSYuP0uIjtLhBi2u0eLfFowXlMa1O0uM-IKrmPj-3TO74tnx2l1tn8tnKkYqP0sBkMALtj-i8ORwtmr4LlItAuQyUq0C5PlD60f2naX8wfD-lmgXwKTulz8E1CKW2n2lBJIm01t8cuq9gBV1Z3852p1_tSDVjU7kIlMtAuQqU6wPlcEbGdiloUnlA5QGVB1QeUPk_qHFAjQNqHFDjgBoH1PgoVaOTu_uO85crBfD7189vENH1f5QwjHoPoKSghwXGhh6PltRipihXZ8PxOfOJ9D5_ceOV24_v9N4ohWvyGJJVC7Xa0PpBfX1g8JynaB8V62O-jYjUidmUFO_Pem5EVvi8lVX0q6z5vi_SYGupcU3DZrKOuqWf_n_gD24e79Y=) -->




### Technical Specifications

- **Distributed Global Architecture**: Implements a globally distributed system with target latency of sub-50ms for data retrieval operations.
- **Geographic Optimization**: Implements location-aware content delivery that prioritizes local data access for performance optimization.
- **Post-Quantum Security**: Incorporates cryptographic protocols designed to resist attacks from quantum computing systems.
- **Protocol Interoperability**: Implements compatibility with IPFS and other distributed storage protocols.
- **Resource Efficiency**: Achieves 3-10x resource efficiency compared to conventional content delivery architectures.
- **Data Integrity Protection**: Inherits the data integrity and corruption resistance mechanisms from the Quantum Safe Storage system.

### Implementation Status

FungiStor will function as the backend infrastructure for Flists within the distributed system architecture. The implementation is designed to be protocol-agnostic, making it suitable for any global-scale content delivery requirements for files, objects, and images.

This system will be integrated with the Mycelium Network architecture.

> Note: The technical specification is subject to refinement, and the name may be revised before final implementation.
