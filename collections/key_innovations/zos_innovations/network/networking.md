---
sidebar_position: 1
title: 'Zero-OS Network System'
description: 'Technical architecture of the Zero-OS distributed network system'
---

# Zero-OS Network System

The Zero-OS network system implements a distributed peer-to-peer networking architecture designed for secure, resilient connectivity within decentralized environments.

## Core Architectural Principles

1. **Secure Mesh Overlay**: Implements a fully distributed peer-to-peer network topology where participants can securely communicate with all others.

2. **End-to-End Encryption**: All communication between network participants is encrypted from source to destination.

3. **Network Isolation**: Workloads operate within isolated private networks by default, with no automatic public internet connectivity.

4. **Controlled Public Exposure**: Access to public networks is provided through explicitly configured Web Gateway components.

5. **Optimized Path Routing**: Dynamic traffic routing algorithms identify the most efficient path between network participants.

## Network Component Hierarchy

The Zero-OS network system employs a layered architecture:

1. **ZNet**: Foundational networking layer implementing a virtual private data center model, enabling full mesh connectivity between containerized workloads.

2. **Mycelium Network**: Distributed overlay network operating on top of existing Internet infrastructure, providing secure end-to-end encrypted communication.

3. **Web Gateway**: Specialized component connecting private networks to the public Internet while maintaining isolation between internet-facing services and secure backend workloads.

## Technical Implementation

### Secure Mesh Overlay (ZNet)

ZNet forms the foundational layer for distributed grid architectures:

- Enables full mesh connectivity where each container can establish secure connections with all other containers
- Implements a single-tenant model with no default public Internet connectivity
- Maintains isolation from public Internet infrastructure

### Mycelium Network

Mycelium implements a sophisticated overlay network:

- Network agents establish secure connections with other participants
- Traffic is routed through the optimal path between participants
- End-to-end encryption secures all communications
- Dynamically routes traffic across multiple connection types to maintain persistent sessions
- Implements cryptographic security and authentication verification
- Achieves throughput of up to 1 Gbps per Network Agent

### Web Gateway

The Web Gateway provides secure public Internet exposure:

- Creates architectural separation between compute workload execution and service exposure
- Requires namespace reservation on distributed ledger
- Configures proxy service for reserved namespace
- Implements TLS configuration and security parameters
- Supports multi-gateway redundancy and clustering
- Maintains connection persistence during container relocation

## Security Implementation

The system implements advanced security beyond traditional perimeter defense:

- Cryptographic authentication at the endpoint level
- Peer-to-peer communication through authenticated secure channels
- Optional whitelist-based access control with group permission structures
- Entity whitelisting based on cryptographic authentication credentials

## Current Implementation Status

- **ZNet**: Production implementation
- **Web Gateway**: Production implementation
- **Mycelium Network**: Beta testing phase (available from version 3.13)
- **Distributed Access Control**: Available in enterprise edition
