---
sidebar_position: 3
title: 'Web Gateway Architecture'
description: 'Technical implementation of the Zero-OS Web Gateway for secure public network exposure'
---

# Web Gateway Architecture

The Web Gateway is a specialized component of the Zero-OS network system that provides a secure mechanism for exposing private network services to the public Internet while maintaining strict isolation between Internet-facing services and backend workloads.

## Technical Implementation

The Web Gateway creates a clear architectural separation between compute workload execution and service exposure points:

1. **Gateway Nodes**: Infrastructure providers configure nodes with gateway capability, allowing them to accept gateway workloads and implement traffic forwarding.

2. **Namespace Reservation**: Each gateway service requires a namespace reservation (prefix) on the distributed ledger, establishing a unique identifier for the service.

3. **Proxy Configuration**: Upon workload assignment, the node configures a proxy service for the reserved namespace, routing traffic to the appropriate internal endpoints.

4. **TLS Implementation**: Security parameters control TLS configuration and other connection properties to ensure secure communication.

This architecture maintains isolation between Internet-facing services and the secure workloads running in Zero VMs, reducing the attack surface for backend systems.

## Redundancy Architecture

The Web Gateway implements several redundancy mechanisms to ensure high availability:

1. **Multi-Gateway Redundancy**: Applications can utilize multiple web gateways simultaneously, with DNS round-robin for load distribution and connection redundancy.

2. **Clustering Architecture**: Gateway services can be clustered to enable continuous service availability despite individual gateway or node failures.

3. **Connection Persistence**: When containers are relocated or recreated, existing end-user connections remain valid as they terminate at the Web Gateway layer, with relocated containers establishing new connections to resume traffic processing.

![](img/redundant_net.jpg)

This approach enables robust service availability while simplifying maintenance operations and providing resilience against failures.

## Horizontal Scalability

The Web Gateway architecture implements a horizontally scalable system with no inherent bottlenecks:

![](img/webgw_scaling.jpg)

1. **Independent Scaling**: Network capacity (supply) and utilization (demand) scale independently, allowing the system to adapt to changing requirements.

2. **Unlimited Provider Support**: The system supports unlimited infrastructure providers offering web gateway services alongside compute and storage resources.

3. **Distributed Load**: Traffic is distributed across multiple gateways, preventing concentration of load at any single point in the network.

This scalable approach enables the system to grow organically as demand increases, with no architectural limitations on size or capacity.

## Technical Capabilities

### Multi-Protocol Support

The Web Gateway supports multiple protocols and connection types:

- HTTP/HTTPS traffic with TLS termination
- TCP forwarding for custom protocols
- UDP support for real-time applications
- WebSocket connections for bidirectional communication

### IPv4 Conservation

The architecture implements efficient IPv4 address utilization through:

- Shared IPv4 addressing across multiple services
- Domain-based routing using HTTP host headers
- SNI-based TLS routing for HTTPS traffic
- Port-based routing for non-HTTP protocols

### Security Implementation

Backend Zero VMs communicate with Web Gateways through secure channels:

- Planetary Network (Mycelium) addressing
- IPv6 support for direct connectivity
- End-to-end encrypted communication
- Optional mutual TLS authentication

Security responsibility is distributed:
- Gateway nodes handle TLS termination and connection security
- Zero VM administrators implement appropriate access controls and port restrictions

## Implementation Status

The Web Gateway is a production-ready component, fully implemented and actively used in Zero-OS deployments. It provides a critical function in bridging between secure private workloads and public Internet exposure.
