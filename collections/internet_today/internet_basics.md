---
title: 'More Than Cables'
sidebar_position: 2
---

# Internet is More Than the Cables



![](img/3layers.png)

**The Internet is made up of 3 layers:**

1. Compute, AI & Storage: this is where applications are being served from. Currently this system is highly centralized and runs from large data centers (see below).

2. Network: This is the ability for information to travel and it can be wireless, via cables (fiber) or satellite links etc. Currently information needs to travel very far and, for most countries, very little information is stored locally. A handful of companies own more than 80% of the current Internet's network capacity. 

3. Applications: Currently applications are hosted in huge data centers using the compute and storage as provided. This system is too centralized and therefore very vulnerable.

We are providing a more optimized solution for the first 2 layers and allows everyone else to build on top.

## The Role of Internet Cables

Digital information mainly travels over large fiber backbone links as pictured below.

![](img/internet_cables.png)

The Internet as we know it has significantly diverged from its original intent. For example, if two people in Zanzibar, an Island in Africa, use Zoom with each other then the information will travel from Zanzibar to a large European datacenter where the Zoom servers are being hosted, and back again.

This leads to very inneficient behavior, slower performance, less reliability and a cost which is higher than what it should be.

![](img/absurd.png)


## The Role of Datacenters

![](img/this_is_our_internet.png)

The internet applications stacks are replicated many times for the various applications we use, each requiring its own full infrastructure. This approach is unsustainable and inefficient.

## Issues with Autonomy and Autonomy

Our current internet model compromises autonomy. Most data is stored in large data centers controlled by a few major corporations, effectively turning users into products.

