# ThreeFold Documentation - HeroScript Configuration Guide

This guide explains how to use the generated HeroScript configurations for the ThreeFold TFGrid4 documentation project.

## Overview

The documentation project consists of multiple sections, each with its own HeroScript configuration:

- **Main Documentation** (`main/cfg/basic.heroscript`) - Core ThreeFold DePIN docs
- **Tech Documentation** (`tech/cfg/basic.heroscript`) - Technical specifications
- **Mycelium Documentation** (`mycelium/cfg/basic.heroscript`) - Network overlay docs
- **Master Configuration** (`master.heroscript`) - Orchestrates all sections

## Quick Start

### Deploy Individual Sections

```bash
# Deploy main documentation locally
hero docusaurus -path main/cfg/basic.heroscript -d

# Deploy tech documentation locally  
hero docusaurus -path tech/cfg/basic.heroscript -d

# Deploy mycelium documentation locally
hero docusaurus -path mycelium/cfg/basic.heroscript -d
```

### Deploy All Sections

```bash
# Deploy all documentation sections using master config
hero docusaurus -path master.heroscript -d
```

## Configuration Details

### Main Documentation Features

- **Comprehensive Navigation**: Structured sidebar with all main sections
- **External Content Import**: Imports GeoMind documentation from external repository
- **Multi-environment Deployment**: Separate dev and production configurations
- **SEO Optimization**: Complete metadata and social media tags
- **Custom Theming**: ThreeFold brand colors and styling

### Key HeroScript Commands Used

1. **Basic Configuration**
   ```heroscript
   !!docusaurus.config
       name:"tfgrid-docs"
       title:"ThreeFold DePIN"
       url:"https://docs.threefold.io"
   ```

2. **Navigation Setup**
   ```heroscript
   !!docusaurus.navbar
       title:""
       logo_src:"img/logo.svg"
   
   !!docusaurus.sidebar_item
       type:"category"
       label:"How It Works"
       items:["howitworks/how_it_works", "howitworks/participants"]
   ```

3. **Content Import**
   ```heroscript
   !!docusaurus.import_source
       url:"https://git.ourworld.tf/..."
       dest:"docs"
       label:"GeoMind"
   ```

4. **Deployment**
   ```heroscript
   !!docusaurus.publish
       cat:"prod"
       dest:"<EMAIL>:/root/hero/www/info/tfgrid4"
   ```

## Deployment Commands

### Development Environment

```bash
# Main docs development
hero docusaurus -path main/cfg/basic.heroscript -d

# Tech docs development
hero docusaurus -path tech/cfg/basic.heroscript -d

# All sections development
hero docusaurus -path master.heroscript -d
```

### Production Deployment

```bash
# Deploy main docs to production
hero docusaurus -path main/cfg/basic.heroscript -bp

# Deploy tech docs to production
hero docusaurus -path tech/cfg/basic.heroscript -bp

# Deploy all sections to production
hero docusaurus -path master.heroscript -bp
```

### Build and Push (Development)

```bash
# Build and push main docs to development
hero docusaurus -path main/cfg/basic.heroscript -bpd

# Build and push all sections to development
hero docusaurus -path master.heroscript -bpd
```

## Customization

### Adding New Sections

1. Create a new directory structure (e.g., `newsection/`)
2. Add `cfg/basic.heroscript` with appropriate configuration
3. Update `master.heroscript` to include the new section
4. Add navigation links in relevant configurations

### Modifying Navigation

Edit the sidebar configuration in the respective `basic.heroscript` file:

```heroscript
!!docusaurus.sidebar_item
    type:"category"
    label:"New Section"
    position:13
    items:["newsection/intro", "newsection/guide"]
```

### Updating Themes

Modify the theme configuration:

```heroscript
!!docusaurus.theme
    primary_color:"#1B4F72"
    secondary_color:"#2E86AB"
    dark_mode:true
```

## URLs and Access

- **Production Main**: https://docs.threefold.io
- **Development Main**: https://dev.threefold.info
- **Tech Docs**: https://threefold.info/tech/
- **Mycelium Docs**: https://threefold.info/mycelium/

## Troubleshooting

### Common Issues

1. **Build Failures**: Check that all referenced files exist in the docs directories
2. **Navigation Issues**: Verify sidebar item paths match actual file locations
3. **Import Failures**: Ensure external repository URLs are accessible
4. **Deployment Issues**: Verify SSH access to deployment servers

### Debug Commands

```bash
# Verbose output for debugging
hero docusaurus -path main/cfg/basic.heroscript -d -v

# Check configuration syntax
hero docusaurus -path main/cfg/basic.heroscript --validate
```

## Next Steps

1. Test all configurations locally
2. Verify navigation and content structure
3. Update any missing content or broken links
4. Deploy to development environment for testing
5. Deploy to production when ready

For more information about HeroScript and Docusaurus integration, see the `docusaurus_info.md` file.
